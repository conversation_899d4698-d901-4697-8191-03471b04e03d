# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: mineru
on:
  push:
    branches:
      - "master"
      - "dev"
    paths-ignore:
      - "cmds/**"
      - "**.md"
jobs:
  cli-test:
    if: github.repository == 'opendatalab/MinerU'
    runs-on: pdf
    timeout-minutes: 240
    strategy:
      fail-fast: true

    steps:
    - name: PDF cli
      uses: actions/checkout@v3
      with:
        fetch-depth: 2

    - name: install&test
      run: |
        source activate mineru
        conda env list
        pip show coverage
        cd $GITHUB_WORKSPACE && sh tests/retry_env.sh
        cd $GITHUB_WORKSPACE && python tests/clean_coverage.py      
        cd $GITHUB_WORKSPACE && coverage run -m pytest tests/unittest/ --cov=magic_pdf/  --cov-report html --cov-report term-missing
        cd $GITHUB_WORKSPACE && python tests/get_coverage.py
        cd $GITHUB_WORKSPACE && pytest -s -v tests/test_cli/test_cli_sdk.py

  notify_to_feishu:
    if: ${{ always() && !cancelled() && contains(needs.*.result, 'failure')}}
    needs: cli-test
    runs-on: pdf
    steps:
    - name: get_actor
      run: |
          metion_list="dt-yy"
          echo $GITHUB_ACTOR
          if [[ $GITHUB_ACTOR == "drunkpig" ]]; then
            metion_list="xuchao"
          elif [[ $GITHUB_ACTOR == "myhloli" ]]; then
            metion_list="zhaoxiaomeng"
          elif [[ $GITHUB_ACTOR == "icecraft" ]]; then
            metion_list="xurui1"
          fi
          echo $metion_list
          echo "METIONS=$metion_list" >> "$GITHUB_ENV"
          echo ${{ env.METIONS }}

    - name: notify
      run: |
        #echo ${{ secrets.USER_ID }}
        curl -X POST -H "Content-Type: application/json" -d '{"msg_type":"post","content":{"post":{"zh_cn":{"title":"'${{ github.repository }}' GitHubAction Failed","content":[[{"tag":"text","text":""},{"tag":"a","text":"Please click here for details ","href":"https://github.com/'${{ github.repository }}'/actions/runs/'${GITHUB_RUN_ID}'"},{"tag":"at","user_id":"'$USER_ID'"}]]}}}}'  $WEBHOOK_URL
