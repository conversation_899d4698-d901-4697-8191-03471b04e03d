{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host ", "build": "tsc --noEmit && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@codemirror/view": "^6.33.0", "@tanstack/react-query": "^5.56.2", "@types/lodash": "^4.17.7", "@types/qs": "^6.9.15", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/codemirror-extensions-langs": "^4.23.0", "@uiw/react-codemirror": "^4.23.0", "ahooks": "^3.8.1", "antd": "^5.20.3", "axios": "^1.7.5", "canvas": "^2.11.2", "classnames": "^2.5.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "path2d": "^0.2.1", "qs": "^6.13.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-intl": "^6.6.8", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-router-dom": "^6.26.1", "react-syntax-highlighter": "^15.5.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.5.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "less": "^4.2.0", "postcss": "^8.4.41", "sass-embedded": "^1.77.8", "tailwindcss": "^3.4.10", "ts-prune": "^0.10.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}