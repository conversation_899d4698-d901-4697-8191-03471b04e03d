/* Copyright 2014 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

 /* custom */
 .tooltip {
  position: absolute;
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 100;
  pointer-events: none;
}
.tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #333;
}


.textLayer{
  position:absolute;
  text-align:initial;
  inset:0;
  overflow:hidden;
  opacity:1;
  line-height:1;
  -webkit-text-size-adjust:none;
     -moz-text-size-adjust:none;
          text-size-adjust:none;
  forced-color-adjust:none;
  transform-origin:0 0;
  caret-color:CanvasText;
}

.textLayer.highlighting{
    touch-action:none;
  }

.textLayer :is(span, br){
    color:transparent;
    /* color: rgba(18, 19, 22, 80%); */
    position:absolute;
    white-space:pre;
    cursor:text;
    transform-origin:0% 0%;
    z-index: 999;
  }

.textLayer span.markedContent{
    top:0;
    height:0;
  }

.textLayer .highlight{
    --highlight-bg-color:rgb(180 0 170 / 0.25);
    --highlight-selected-bg-color:rgb(0 100 0 / 0.25);
    --highlight-backdrop-filter:none;
    --highlight-selected-backdrop-filter:none;

    margin:-1px;
    padding:1px;
    background-color:var(--highlight-bg-color);
    -webkit-backdrop-filter:var(--highlight-backdrop-filter);
            backdrop-filter:var(--highlight-backdrop-filter);
    border-radius:4px;
  }

@media screen and (forced-colors: active){

.textLayer .highlight{
      --highlight-bg-color:transparent;
      --highlight-selected-bg-color:transparent;
      --highlight-backdrop-filter:var(--hcm-highlight-filter);
      --highlight-selected-backdrop-filter:var(
        --hcm-highlight-selected-filter
      );
  }
    }

.textLayer .highlight.appended{
      position:initial;
    }

.textLayer .highlight.begin{
      border-radius:4px 0 0 4px;
    }

.textLayer .highlight.end{
      border-radius:0 4px 4px 0;
    }

.textLayer .highlight.middle{
      border-radius:0;
    }

.textLayer .highlight.selected{
      background-color:var(--highlight-selected-bg-color);
      -webkit-backdrop-filter:var(--highlight-selected-backdrop-filter);
              backdrop-filter:var(--highlight-selected-backdrop-filter);
    }

.textLayer ::-moz-selection{
    background:rgba(0 0 255 / 0.25);
    background:color-mix(in srgb, AccentColor, transparent 75%);
  }

.textLayer ::selection{
    background:rgba(0 0 255 / 0.25);
    background:color-mix(in srgb, AccentColor, transparent 75%);
  }

.textLayer br::-moz-selection{
    background:transparent;
  }

.textLayer br::selection{
    background:transparent;
  }

.textLayer .endOfContent{
    display:block;
    position:absolute;
    inset:100% 0 0;
    z-index:-1;
    cursor:default;
    -webkit-user-select:none;
       -moz-user-select:none;
            user-select:none;
  }

.textLayer .endOfContent.active{
      top:0;
    }

.annotationLayer{
  --annotation-unfocused-field-background:url("data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>");
  --input-focus-border-color:Highlight;
  --input-focus-outline:1px solid Canvas;
  --input-unfocused-border-color:transparent;
  --input-disabled-border-color:transparent;
  --input-hover-border-color:black;
  --link-outline:none;

  position:absolute;
  top:0;
  left:0;
  pointer-events:none;
  transform-origin:0 0;
}

@media screen and (forced-colors: active){

.annotationLayer{
    --input-focus-border-color:CanvasText;
    --input-unfocused-border-color:ActiveText;
    --input-disabled-border-color:GrayText;
    --input-hover-border-color:Highlight;
    --link-outline:1.5px solid LinkText;
}

    .annotationLayer .textWidgetAnnotation :is(input, textarea):required, .annotationLayer .choiceWidgetAnnotation select:required, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required{
      outline:1.5px solid selectedItem;
    }

    .annotationLayer .linkAnnotation{
      outline:var(--link-outline);
    }

      .annotationLayer .linkAnnotation:hover{
        -webkit-backdrop-filter:var(--hcm-highlight-filter);
                backdrop-filter:var(--hcm-highlight-filter);
      }

      .annotationLayer .linkAnnotation > a:hover{
        opacity:0 !important;
        background:none !important;
        box-shadow:none;
      }

    .annotationLayer .popupAnnotation .popup{
      outline:calc(1.5px * var(--scale-factor)) solid CanvasText !important;
      background-color:ButtonFace !important;
      color:ButtonText !important;
    }

    .annotationLayer .highlightArea:hover::after{
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      -webkit-backdrop-filter:var(--hcm-highlight-filter);
              backdrop-filter:var(--hcm-highlight-filter);
      content:"";
      pointer-events:none;
    }

    .annotationLayer .popupAnnotation.focused .popup{
      outline:calc(3px * var(--scale-factor)) solid Highlight !important;
    }
  }

.annotationLayer[data-main-rotation="90"] .norotate{
    transform:rotate(270deg) translateX(-100%);
  }

.annotationLayer[data-main-rotation="180"] .norotate{
    transform:rotate(180deg) translate(-100%, -100%);
  }

.annotationLayer[data-main-rotation="270"] .norotate{
    transform:rotate(90deg) translateY(-100%);
  }

.annotationLayer.disabled section,
    .annotationLayer.disabled .popup{
      pointer-events:none;
    }

.annotationLayer .annotationContent{
    position:absolute;
    width:100%;
    height:100%;
    pointer-events:none;
  }

.annotationLayer .annotationContent.freetext{
      background:transparent;
      border:none;
      inset:0;
      overflow:visible;
      white-space:nowrap;
      font:10px sans-serif;
      line-height:1.35;
      -webkit-user-select:none;
         -moz-user-select:none;
              user-select:none;
    }

.annotationLayer section{
    position:absolute;
    text-align:initial;
    pointer-events:auto;
    box-sizing:border-box;
    transform-origin:0 0;
  }

.annotationLayer section:has(div.annotationContent) canvas.annotationContent{
        display:none;
      }

.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton) > a{
    position:absolute;
    font-size:1em;
    top:0;
    left:0;
    width:100%;
    height:100%;
  }

.annotationLayer :is(.linkAnnotation, .buttonWidgetAnnotation.pushButton):not(.hasBorder)
    > a:hover{
    opacity:0.2;
    background-color:rgb(255 255 0);
    box-shadow:0 2px 10px rgb(255 255 0);
  }

.annotationLayer .linkAnnotation.hasBorder:hover{
    background-color:rgb(255 255 0 / 0.2);
  }

.annotationLayer .hasBorder{
    background-size:100% 100%;
  }

.annotationLayer .textAnnotation img{
    position:absolute;
    cursor:pointer;
    width:100%;
    height:100%;
    top:0;
    left:0;
  }

.annotationLayer .textWidgetAnnotation :is(input, textarea), .annotationLayer .choiceWidgetAnnotation select, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input{
    background-image:var(--annotation-unfocused-field-background);
    border:2px solid var(--input-unfocused-border-color);
    box-sizing:border-box;
    font:calc(9px * var(--scale-factor)) sans-serif;
    height:100%;
    margin:0;
    vertical-align:top;
    width:100%;
  }

.annotationLayer .textWidgetAnnotation :is(input, textarea):required, .annotationLayer .choiceWidgetAnnotation select:required, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:required{
    outline:1.5px solid red;
  }

.annotationLayer .choiceWidgetAnnotation select option{
    padding:0;
  }

.annotationLayer .buttonWidgetAnnotation.radioButton input{
    border-radius:50%;
  }

.annotationLayer .textWidgetAnnotation textarea{
    resize:none;
  }

.annotationLayer .textWidgetAnnotation [disabled]:is(input, textarea), .annotationLayer .choiceWidgetAnnotation select[disabled], .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input[disabled]{
    background:none;
    border:2px solid var(--input-disabled-border-color);
    cursor:not-allowed;
  }

.annotationLayer .textWidgetAnnotation :is(input, textarea):hover, .annotationLayer .choiceWidgetAnnotation select:hover, .annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input:hover{
    border:2px solid var(--input-hover-border-color);
  }

.annotationLayer .textWidgetAnnotation :is(input, textarea):hover, .annotationLayer .choiceWidgetAnnotation select:hover, .annotationLayer .buttonWidgetAnnotation.checkBox input:hover{
    border-radius:2px;
  }

.annotationLayer .textWidgetAnnotation :is(input, textarea):focus, .annotationLayer .choiceWidgetAnnotation select:focus{
    background:none;
    border:2px solid var(--input-focus-border-color);
    border-radius:2px;
    outline:var(--input-focus-outline);
  }

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) :focus{
    background-image:none;
    background-color:transparent;
  }

.annotationLayer .buttonWidgetAnnotation.checkBox :focus{
    border:2px solid var(--input-focus-border-color);
    border-radius:2px;
    outline:var(--input-focus-outline);
  }

.annotationLayer .buttonWidgetAnnotation.radioButton :focus{
    border:2px solid var(--input-focus-border-color);
    outline:var(--input-focus-outline);
  }

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before,
  .annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after,
  .annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before{
    background-color:CanvasText;
    content:"";
    display:block;
    position:absolute;
  }

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before,
  .annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after{
    height:80%;
    left:45%;
    width:1px;
  }

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::before{
    transform:rotate(45deg);
  }

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked::after{
    transform:rotate(-45deg);
  }

.annotationLayer .buttonWidgetAnnotation.radioButton input:checked::before{
    border-radius:50%;
    height:50%;
    left:25%;
    top:25%;
    width:50%;
  }

.annotationLayer .textWidgetAnnotation input.comb{
    font-family:monospace;
    padding-left:2px;
    padding-right:0;
  }

.annotationLayer .textWidgetAnnotation input.comb:focus{
    width:103%;
  }

.annotationLayer .buttonWidgetAnnotation:is(.checkBox, .radioButton) input{
    -webkit-appearance:none;
       -moz-appearance:none;
            appearance:none;
  }

.annotationLayer .fileAttachmentAnnotation .popupTriggerArea{
    height:100%;
    width:100%;
  }

.annotationLayer .popupAnnotation{
    position:absolute;
    font-size:calc(9px * var(--scale-factor));
    pointer-events:none;
    width:-moz-max-content;
    width:max-content;
    max-width:45%;
    height:auto;
  }

.annotationLayer .popup{
    background-color:rgb(255 255 153);
    box-shadow:0 calc(2px * var(--scale-factor)) calc(5px * var(--scale-factor)) rgb(136 136 136);
    border-radius:calc(2px * var(--scale-factor));
    outline:1.5px solid rgb(255 255 74);
    padding:calc(6px * var(--scale-factor));
    cursor:pointer;
    font:message-box;
    white-space:normal;
    word-wrap:break-word;
    pointer-events:auto;
  }

.annotationLayer .popupAnnotation.focused .popup{
    outline-width:3px;
  }

.annotationLayer .popup *{
    font-size:calc(9px * var(--scale-factor));
  }

.annotationLayer .popup > .header{
    display:inline-block;
  }

.annotationLayer .popup > .header h1{
    display:inline;
  }

.annotationLayer .popup > .header .popupDate{
    display:inline-block;
    margin-left:calc(5px * var(--scale-factor));
    width:-moz-fit-content;
    width:fit-content;
  }

.annotationLayer .popupContent{
    border-top:1px solid rgb(51 51 51);
    margin-top:calc(2px * var(--scale-factor));
    padding-top:calc(2px * var(--scale-factor));
  }

.annotationLayer .richText > *{
    white-space:pre-wrap;
    font-size:calc(9px * var(--scale-factor));
  }

.annotationLayer .popupTriggerArea{
    cursor:pointer;
  }

.annotationLayer section svg{
    position:absolute;
    width:100%;
    height:100%;
    top:0;
    left:0;
  }

.annotationLayer .annotationTextContent{
    position:absolute;
    width:100%;
    height:100%;
    opacity:0;
    color:transparent;
    -webkit-user-select:none;
       -moz-user-select:none;
            user-select:none;
    pointer-events:none;
  }

.annotationLayer .annotationTextContent span{
      width:100%;
      display:inline-block;
    }

.annotationLayer svg.quadrilateralsContainer{
    contain:strict;
    width:0;
    height:0;
    position:absolute;
    top:0;
    left:0;
    z-index:-1;
  }

:root{
  --xfa-unfocused-field-background:url("data:image/svg+xml;charset=UTF-8,<svg width='1px' height='1px' xmlns='http://www.w3.org/2000/svg'><rect width='100%' height='100%' style='fill:rgba(0, 54, 255, 0.13);'/></svg>");
  --xfa-focus-outline:auto;
}

@media screen and (forced-colors: active){
  :root{
    --xfa-focus-outline:2px solid CanvasText;
  }
  .xfaLayer *:required{
    outline:1.5px solid selectedItem;
  }
}

.xfaLayer{
  background-color:transparent;
}

.xfaLayer .highlight{
  margin:-1px;
  padding:1px;
  background-color:rgb(239 203 237);
  border-radius:4px;
}

.xfaLayer .highlight.appended{
  position:initial;
}

.xfaLayer .highlight.begin{
  border-radius:4px 0 0 4px;
}

.xfaLayer .highlight.end{
  border-radius:0 4px 4px 0;
}

.xfaLayer .highlight.middle{
  border-radius:0;
}

.xfaLayer .highlight.selected{
  background-color:rgb(203 223 203);
}

.xfaPage{
  overflow:hidden;
  position:relative;
}

.xfaContentarea{
  position:absolute;
}

.xfaPrintOnly{
  display:none;
}

.xfaLayer{
  position:absolute;
  text-align:initial;
  top:0;
  left:0;
  transform-origin:0 0;
  line-height:1.2;
}

.xfaLayer *{
  color:inherit;
  font:inherit;
  font-style:inherit;
  font-weight:inherit;
  font-kerning:inherit;
  letter-spacing:-0.01px;
  text-align:inherit;
  text-decoration:inherit;
  box-sizing:border-box;
  background-color:transparent;
  padding:0;
  margin:0;
  pointer-events:auto;
  line-height:inherit;
}

.xfaLayer *:required{
  outline:1.5px solid red;
}

.xfaLayer div,
.xfaLayer svg,
.xfaLayer svg *{
  pointer-events:none;
}

.xfaLayer a{
  color:blue;
}

.xfaRich li{
  margin-left:3em;
}

.xfaFont{
  color:black;
  font-weight:normal;
  font-kerning:none;
  font-size:10px;
  font-style:normal;
  letter-spacing:0;
  text-decoration:none;
  vertical-align:0;
}

.xfaCaption{
  overflow:hidden;
  flex:0 0 auto;
}

.xfaCaptionForCheckButton{
  overflow:hidden;
  flex:1 1 auto;
}

.xfaLabel{
  height:100%;
  width:100%;
}

.xfaLeft{
  display:flex;
  flex-direction:row;
  align-items:center;
}

.xfaRight{
  display:flex;
  flex-direction:row-reverse;
  align-items:center;
}

:is(.xfaLeft, .xfaRight) > :is(.xfaCaption, .xfaCaptionForCheckButton){
  max-height:100%;
}

.xfaTop{
  display:flex;
  flex-direction:column;
  align-items:flex-start;
}

.xfaBottom{
  display:flex;
  flex-direction:column-reverse;
  align-items:flex-start;
}

:is(.xfaTop, .xfaBottom) > :is(.xfaCaption, .xfaCaptionForCheckButton){
  width:100%;
}

.xfaBorder{
  background-color:transparent;
  position:absolute;
  pointer-events:none;
}

.xfaWrapped{
  width:100%;
  height:100%;
}

:is(.xfaTextfield, .xfaSelect):focus{
  background-image:none;
  background-color:transparent;
  outline:var(--xfa-focus-outline);
  outline-offset:-1px;
}

:is(.xfaCheckbox, .xfaRadio):focus{
  outline:var(--xfa-focus-outline);
}

.xfaTextfield,
.xfaSelect{
  height:100%;
  width:100%;
  flex:1 1 auto;
  border:none;
  resize:none;
  background-image:var(--xfa-unfocused-field-background);
}

.xfaSelect{
  padding-inline:2px;
}

:is(.xfaTop, .xfaBottom) > :is(.xfaTextfield, .xfaSelect){
  flex:0 1 auto;
}

.xfaButton{
  cursor:pointer;
  width:100%;
  height:100%;
  border:none;
  text-align:center;
}

.xfaLink{
  width:100%;
  height:100%;
  position:absolute;
  top:0;
  left:0;
}

.xfaCheckbox,
.xfaRadio{
  width:100%;
  height:100%;
  flex:0 0 auto;
  border:none;
}

.xfaRich{
  white-space:pre-wrap;
  width:100%;
  height:100%;
}

.xfaImage{
  -o-object-position:left top;
     object-position:left top;
  -o-object-fit:contain;
     object-fit:contain;
  width:100%;
  height:100%;
}

.xfaLrTb,
.xfaRlTb,
.xfaTb{
  display:flex;
  flex-direction:column;
  align-items:stretch;
}

.xfaLr{
  display:flex;
  flex-direction:row;
  align-items:stretch;
}

.xfaRl{
  display:flex;
  flex-direction:row-reverse;
  align-items:stretch;
}

.xfaTb > div{
  justify-content:left;
}

.xfaPosition{
  position:relative;
}

.xfaArea{
  position:relative;
}

.xfaValignMiddle{
  display:flex;
  align-items:center;
}

.xfaTable{
  display:flex;
  flex-direction:column;
  align-items:stretch;
}

.xfaTable .xfaRow{
  display:flex;
  flex-direction:row;
  align-items:stretch;
}

.xfaTable .xfaRlRow{
  display:flex;
  flex-direction:row-reverse;
  align-items:stretch;
  flex:1;
}

.xfaTable .xfaRlRow > div{
  flex:1;
}

:is(.xfaNonInteractive, .xfaDisabled, .xfaReadOnly) :is(input, textarea){
  background:initial;
}

@media print{
  .xfaTextfield,
  .xfaSelect{
    background:transparent;
  }

  .xfaSelect{
    -webkit-appearance:none;
       -moz-appearance:none;
            appearance:none;
    text-indent:1px;
    text-overflow:"";
  }
}

/* .canvasWrapper svg{
    transform:none;
  }

.canvasWrapper svg[data-main-rotation="90"] mask,
      .canvasWrapper svg[data-main-rotation="90"] use:not(.clip, .mask){
        transform:matrix(0, 1, -1, 0, 1, 0);
      }

.canvasWrapper svg[data-main-rotation="180"] mask,
      .canvasWrapper svg[data-main-rotation="180"] use:not(.clip, .mask){
        transform:matrix(-1, 0, 0, -1, 1, 1);
      }

.canvasWrapper svg[data-main-rotation="270"] mask,
      .canvasWrapper svg[data-main-rotation="270"] use:not(.clip, .mask){
        transform:matrix(0, -1, 1, 0, 0, 1);
      }

.canvasWrapper svg.highlight{
      --blend-mode:multiply;

      position:absolute;
      mix-blend-mode:var(--blend-mode);
    } */

@media screen and (forced-colors: active){

.canvasWrapper svg.highlight{
        --blend-mode:difference;
    }
      }

.canvasWrapper svg.highlight:not(.free){
        fill-rule:evenodd;
      }

.canvasWrapper svg.highlightOutline{
      position:absolute;
      mix-blend-mode:normal;
      fill-rule:evenodd;
      fill:none;
    }

.canvasWrapper svg.highlightOutline.hovered:not(.free):not(.selected){
          stroke:var(--hover-outline-color);
          stroke-width:var(--outline-width);
        }

.canvasWrapper svg.highlightOutline.selected:not(.free) .mainOutline{
            stroke:var(--outline-around-color);
            stroke-width:calc(
              var(--outline-width) + 2 * var(--outline-around-width)
            );
          }

.canvasWrapper svg.highlightOutline.selected:not(.free) .secondaryOutline{
            stroke:var(--outline-color);
            stroke-width:var(--outline-width);
          }

.canvasWrapper svg.highlightOutline.free.hovered:not(.selected){
          stroke:var(--hover-outline-color);
          stroke-width:calc(2 * var(--outline-width));
        }

.canvasWrapper svg.highlightOutline.free.selected .mainOutline{
            stroke:var(--outline-around-color);
            stroke-width:calc(
              2 * (var(--outline-width) + var(--outline-around-width))
            );
          }

.canvasWrapper svg.highlightOutline.free.selected .secondaryOutline{
            stroke:var(--outline-color);
            stroke-width:calc(2 * var(--outline-width));
          }

.toggle-button{
  --button-background-color:#f0f0f4;
  --button-background-color-hover:#e0e0e6;
  --button-background-color-active:#cfcfd8;
  --color-accent-primary:#0060df;
  --color-accent-primary-hover:#0250bb;
  --color-accent-primary-active:#054096;
  --border-interactive-color:#8f8f9d;
  --border-radius-circle:9999px;
  --border-width:1px;
  --size-item-small:16px;
  --size-item-large:32px;
  --color-canvas:white;

  --toggle-background-color:var(--button-background-color);
  --toggle-background-color-hover:var(--button-background-color-hover);
  --toggle-background-color-active:var(--button-background-color-active);
  --toggle-background-color-pressed:var(--color-accent-primary);
  --toggle-background-color-pressed-hover:var(--color-accent-primary-hover);
  --toggle-background-color-pressed-active:var(--color-accent-primary-active);
  --toggle-border-color:var(--border-interactive-color);
  --toggle-border-color-hover:var(--toggle-border-color);
  --toggle-border-color-active:var(--toggle-border-color);
  --toggle-border-radius:var(--border-radius-circle);
  --toggle-border-width:var(--border-width);
  --toggle-height:var(--size-item-small);
  --toggle-width:var(--size-item-large);
  --toggle-dot-background-color:var(--toggle-border-color);
  --toggle-dot-background-color-hover:var(--toggle-dot-background-color);
  --toggle-dot-background-color-active:var(--toggle-dot-background-color);
  --toggle-dot-background-color-on-pressed:var(--color-canvas);
  --toggle-dot-margin:1px;
  --toggle-dot-height:calc(
    var(--toggle-height) - 2 * var(--toggle-dot-margin) - 2 *
      var(--toggle-border-width)
  );
  --toggle-dot-width:var(--toggle-dot-height);
  --toggle-dot-transform-x:calc(
    var(--toggle-width) - 4 * var(--toggle-dot-margin) - var(--toggle-dot-width)
  );

  -webkit-appearance:none;

     -moz-appearance:none;

          appearance:none;
  padding:0;
  margin:0;
  border:var(--toggle-border-width) solid var(--toggle-border-color);
  height:var(--toggle-height);
  width:var(--toggle-width);
  border-radius:var(--toggle-border-radius);
  background:var(--toggle-background-color);
  box-sizing:border-box;
  flex-shrink:0;
}

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) .toggle-button{
    --button-background-color:color-mix(in srgb, currentColor 7%, transparent);
    --button-background-color-hover:color-mix(
      in srgb,
      currentColor 14%,
      transparent
    );
    --button-background-color-active:color-mix(
      in srgb,
      currentColor 21%,
      transparent
    );
    --color-accent-primary:#0df;
    --color-accent-primary-hover:#80ebff;
    --color-accent-primary-active:#aaf2ff;
    --border-interactive-color:#bfbfc9;
    --color-canvas:#1c1b22;
}
  }

:where(html.is-dark) .toggle-button{
    --button-background-color:color-mix(in srgb, currentColor 7%, transparent);
    --button-background-color-hover:color-mix(
      in srgb,
      currentColor 14%,
      transparent
    );
    --button-background-color-active:color-mix(
      in srgb,
      currentColor 21%,
      transparent
    );
    --color-accent-primary:#0df;
    --color-accent-primary-hover:#80ebff;
    --color-accent-primary-active:#aaf2ff;
    --border-interactive-color:#bfbfc9;
    --color-canvas:#1c1b22;
}

@media (forced-colors: active){

.toggle-button{
    --color-accent-primary:ButtonText;
    --color-accent-primary-hover:SelectedItem;
    --color-accent-primary-active:SelectedItem;
    --border-interactive-color:ButtonText;
    --button-background-color:ButtonFace;
    --border-interactive-color-hover:SelectedItem;
    --border-interactive-color-active:SelectedItem;
    --border-interactive-color-disabled:GrayText;
    --color-canvas:ButtonText;
}
  }

.toggle-button:focus-visible{
    outline:var(--focus-outline);
    outline-offset:var(--focus-outline-offset);
  }

.toggle-button:enabled:hover{
    background:var(--toggle-background-color-hover);
    border-color:var(--toggle-border-color);
  }

.toggle-button:enabled:active{
    background:var(--toggle-background-color-active);
    border-color:var(--toggle-border-color);
  }

.toggle-button[aria-pressed="true"]{
    background:var(--toggle-background-color-pressed);
    border-color:transparent;
  }

.toggle-button[aria-pressed="true"]:enabled:hover{
    background:var(--toggle-background-color-pressed-hover);
    border-color:transparent;
  }

.toggle-button[aria-pressed="true"]:enabled:active{
    background:var(--toggle-background-color-pressed-active);
    border-color:transparent;
  }

.toggle-button::before{
    display:block;
    content:"";
    background-color:var(--toggle-dot-background-color);
    height:var(--toggle-dot-height);
    width:var(--toggle-dot-width);
    margin:var(--toggle-dot-margin);
    border-radius:var(--toggle-border-radius);
    translate:0;
  }

.toggle-button[aria-pressed="true"]::before{
    translate:var(--toggle-dot-transform-x);
    background-color:var(--toggle-dot-background-color-on-pressed);
  }

.toggle-button[aria-pressed="true"]:enabled:hover::before,
  .toggle-button[aria-pressed="true"]:enabled:active::before{
    background-color:var(--toggle-dot-background-color-on-pressed);
  }

[dir="rtl"] .toggle-button[aria-pressed="true"]::before{
    translate:calc(-1 * var(--toggle-dot-transform-x));
  }

@media (prefers-reduced-motion: no-preference){
    .toggle-button::before{
      transition:translate 100ms;
    }
  }

@media (prefers-contrast){
    .toggle-button:enabled:hover{
      border-color:var(--toggle-border-color-hover);
    }

    .toggle-button:enabled:active{
      border-color:var(--toggle-border-color-active);
    }

    .toggle-button[aria-pressed="true"]:enabled{
      border-color:var(--toggle-border-color);
      position:relative;
    }

    .toggle-button[aria-pressed="true"]:enabled:hover,
    .toggle-button[aria-pressed="true"]:enabled:hover:active{
      border-color:var(--toggle-border-color-hover);
    }

    .toggle-button[aria-pressed="true"]:enabled:active{
      background-color:var(--toggle-dot-background-color-active);
      border-color:var(--toggle-dot-background-color-hover);
    }

    .toggle-button:hover::before,
    .toggle-button:active::before{
      background-color:var(--toggle-dot-background-color-hover);
    }
  }

@media (forced-colors){

.toggle-button{
    --toggle-dot-background-color:var(--color-accent-primary);
    --toggle-dot-background-color-hover:var(--color-accent-primary-hover);
    --toggle-dot-background-color-active:var(--color-accent-primary-active);
    --toggle-dot-background-color-on-pressed:var(--button-background-color);
    --toggle-background-color-disabled:var(--button-background-color-disabled);
    --toggle-border-color-hover:var(--border-interactive-color-hover);
    --toggle-border-color-active:var(--border-interactive-color-active);
    --toggle-border-color-disabled:var(--border-interactive-color-disabled);
}

    .toggle-button[aria-pressed="true"]:enabled::after{
      border:1px solid var(--button-background-color);
      content:"";
      position:absolute;
      height:var(--toggle-height);
      width:var(--toggle-width);
      display:block;
      border-radius:var(--toggle-border-radius);
      inset:-2px;
    }

    .toggle-button[aria-pressed="true"]:enabled:active::after{
      border-color:var(--toggle-border-color-active);
    }
  }

:root{
  --outline-width:2px;
  --outline-color:#0060df;
  --outline-around-width:1px;
  --outline-around-color:#f0f0f4;
  --hover-outline-around-color:var(--outline-around-color);
  --focus-outline:solid var(--outline-width) var(--outline-color);
  --unfocus-outline:solid var(--outline-width) transparent;
  --focus-outline-around:solid var(--outline-around-width) var(--outline-around-color);
  --hover-outline-color:#8f8f9d;
  --hover-outline:solid var(--outline-width) var(--hover-outline-color);
  --hover-outline-around:solid var(--outline-around-width) var(--hover-outline-around-color);
  --freetext-line-height:1.35;
  --freetext-padding:2px;
  --resizer-bg-color:var(--outline-color);
  --resizer-size:6px;
  --resizer-shift:calc(
    0px - (var(--outline-width) + var(--resizer-size)) / 2 -
      var(--outline-around-width)
  );
  --editorFreeText-editing-cursor:text;
  --editorInk-editing-cursor:url(images/cursor-editorInk.svg) 0 16, pointer;
  --editorHighlight-editing-cursor:url(images/cursor-editorTextHighlight.svg) 24 24, text;
  --editorFreeHighlight-editing-cursor:url(images/cursor-editorFreeHighlight.svg) 1 18, pointer;
}
.visuallyHidden{
  position:absolute;
  top:0;
  left:0;
  border:0;
  margin:0;
  padding:0;
  width:0;
  height:0;
  overflow:hidden;
  white-space:nowrap;
  font-size:0;
}

.textLayer.highlighting{
  cursor:var(--editorFreeHighlight-editing-cursor);
}

.textLayer.highlighting:not(.free) span{
    cursor:var(--editorHighlight-editing-cursor);
  }

.textLayer.highlighting.free span{
    cursor:var(--editorFreeHighlight-editing-cursor);
  }

@media (min-resolution: 1.1dppx){
  :root{
    --editorFreeText-editing-cursor:url(images/cursor-editorFreeText.svg) 0 16, text;
  }
}

@media screen and (forced-colors: active){
  :root{
    --outline-color:CanvasText;
    --outline-around-color:ButtonFace;
    --resizer-bg-color:ButtonText;
    --hover-outline-color:Highlight;
    --hover-outline-around-color:SelectedItemText;
  }
}

[data-editor-rotation="90"]{
  transform:rotate(90deg);
}

[data-editor-rotation="180"]{
  transform:rotate(180deg);
}

[data-editor-rotation="270"]{
  transform:rotate(270deg);
}

.annotationEditorLayer{
  background:transparent;
  position:absolute;
  inset:0;
  font-size:calc(100px * var(--scale-factor));
  transform-origin:0 0;
  cursor:auto;
}

.annotationEditorLayer.waiting{
  content:"";
  cursor:wait;
  position:absolute;
  inset:0;
  width:100%;
  height:100%;
}

.annotationEditorLayer.disabled{
  pointer-events:none;
}

.annotationEditorLayer.freetextEditing{
  cursor:var(--editorFreeText-editing-cursor);
}

.annotationEditorLayer.inkEditing{
  cursor:var(--editorInk-editing-cursor);
}

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor){
  position:absolute;
  background:transparent;
  z-index:1;
  transform-origin:0 0;
  cursor:auto;
  max-width:100%;
  max-height:100%;
  border:var(--unfocus-outline);
}

.annotationEditorLayer .draggable.selectedEditor:is(.freeTextEditor, .inkEditor, .stampEditor){
    cursor:move;
  }

.annotationEditorLayer .moving:is(.freeTextEditor, .inkEditor, .stampEditor){
    touch-action:none;
  }

.annotationEditorLayer .selectedEditor:is(.freeTextEditor, .inkEditor, .stampEditor){
    border:var(--focus-outline);
    outline:var(--focus-outline-around);
  }

.annotationEditorLayer .selectedEditor:is(.freeTextEditor, .inkEditor, .stampEditor)::before{
      content:"";
      position:absolute;
      inset:0;
      border:var(--focus-outline-around);
      pointer-events:none;
    }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor):hover:not(.selectedEditor){
    border:var(--hover-outline);
    outline:var(--hover-outline-around);
  }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor):hover:not(.selectedEditor)::before{
      content:"";
      position:absolute;
      inset:0;
      border:var(--focus-outline-around);
    }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar{
    --editor-toolbar-delete-image:url(images/editor-toolbar-delete.svg);
    --editor-toolbar-bg-color:#f0f0f4;
    --editor-toolbar-highlight-image:url(images/toolbarButton-editorHighlight.svg);
    --editor-toolbar-fg-color:#2e2e56;
    --editor-toolbar-border-color:#8f8f9d;
    --editor-toolbar-hover-border-color:var(--editor-toolbar-border-color);
    --editor-toolbar-hover-bg-color:#e0e0e6;
    --editor-toolbar-hover-fg-color:var(--editor-toolbar-fg-color);
    --editor-toolbar-hover-outline:none;
    --editor-toolbar-focus-outline-color:#0060df;
    --editor-toolbar-shadow:0 2px 6px 0 rgb(58 57 68 / 0.2);
    --editor-toolbar-vert-offset:6px;
    --editor-toolbar-height:28px;
    --editor-toolbar-padding:2px;

    display:flex;
    width:-moz-fit-content;
    width:fit-content;
    height:var(--editor-toolbar-height);
    flex-direction:column;
    justify-content:center;
    align-items:center;
    cursor:default;
    pointer-events:auto;
    box-sizing:content-box;
    padding:var(--editor-toolbar-padding);

    position:absolute;
    inset-inline-end:0;
    inset-block-start:calc(100% + var(--editor-toolbar-vert-offset));

    border-radius:6px;
    background-color:var(--editor-toolbar-bg-color);
    border:1px solid var(--editor-toolbar-border-color);
    box-shadow:var(--editor-toolbar-shadow);
  }

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) :is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar{
      --editor-toolbar-bg-color:#2b2a33;
      --editor-toolbar-fg-color:#fbfbfe;
      --editor-toolbar-hover-bg-color:#52525e;
      --editor-toolbar-focus-outline-color:#0df;
  }
    }

:where(html.is-dark) :is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar{
      --editor-toolbar-bg-color:#2b2a33;
      --editor-toolbar-fg-color:#fbfbfe;
      --editor-toolbar-hover-bg-color:#52525e;
      --editor-toolbar-focus-outline-color:#0df;
  }

@media screen and (forced-colors: active){

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar{
      --editor-toolbar-bg-color:ButtonFace;
      --editor-toolbar-fg-color:ButtonText;
      --editor-toolbar-border-color:ButtonText;
      --editor-toolbar-hover-border-color:AccentColor;
      --editor-toolbar-hover-bg-color:ButtonFace;
      --editor-toolbar-hover-fg-color:AccentColor;
      --editor-toolbar-hover-outline:2px solid var(--editor-toolbar-hover-border-color);
      --editor-toolbar-focus-outline-color:ButtonBorder;
      --editor-toolbar-shadow:none;
  }
    }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar.hidden{
      display:none;
    }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar:has(:focus-visible){
      border-color:transparent;
    }

[dir="ltr"] :is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar{
      transform-origin:100% 0;
    }

[dir="rtl"] :is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar{
      transform-origin:0 0;
    }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons{
      display:flex;
      justify-content:center;
      align-items:center;
      gap:0;
      height:100%;
    }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .divider{
        width:1px;
        height:calc(
          2 * var(--editor-toolbar-padding) + var(--editor-toolbar-height)
        );
        background-color:var(--editor-toolbar-border-color);
        display:inline-block;
        margin-inline:2px;
      }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .highlightButton{
        width:var(--editor-toolbar-height);
      }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .highlightButton::before{
          content:"";
          -webkit-mask-image:var(--editor-toolbar-highlight-image);
                  mask-image:var(--editor-toolbar-highlight-image);
          -webkit-mask-repeat:no-repeat;
                  mask-repeat:no-repeat;
          -webkit-mask-position:center;
                  mask-position:center;
          display:inline-block;
          background-color:var(--editor-toolbar-fg-color);
          width:100%;
          height:100%;
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .highlightButton:hover::before{
          background-color:var(--editor-toolbar-hover-fg-color);
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .delete{
        width:var(--editor-toolbar-height);
      }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .delete::before{
          content:"";
          -webkit-mask-image:var(--editor-toolbar-delete-image);
                  mask-image:var(--editor-toolbar-delete-image);
          -webkit-mask-repeat:no-repeat;
                  mask-repeat:no-repeat;
          -webkit-mask-position:center;
                  mask-position:center;
          display:inline-block;
          background-color:var(--editor-toolbar-fg-color);
          width:100%;
          height:100%;
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .delete:hover::before{
          background-color:var(--editor-toolbar-hover-fg-color);
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons > *{
        height:var(--editor-toolbar-height);
      }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons > :not(.divider){
        border:none;
        background-color:transparent;
        cursor:pointer;
      }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons > :not(.divider):hover{
          border-radius:2px;
          background-color:var(--editor-toolbar-hover-bg-color);
          color:var(--editor-toolbar-hover-fg-color);
          outline:var(--editor-toolbar-hover-outline);
          outline-offset:1px;
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons > :not(.divider):hover:active{
            outline:none;
          }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons > :not(.divider):focus-visible{
          border-radius:2px;
          outline:2px solid var(--editor-toolbar-focus-outline-color);
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText{
        --alt-text-add-image:url(images/altText_add.svg);
        --alt-text-done-image:url(images/altText_done.svg);

        display:flex;
        align-items:center;
        justify-content:center;
        width:-moz-max-content;
        width:max-content;
        padding-inline:8px;
        pointer-events:all;
        font:menu;
        font-weight:590;
        font-size:12px;
        color:var(--editor-toolbar-fg-color);
      }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText:disabled{
          pointer-events:none;
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText::before{
          content:"";
          -webkit-mask-image:var(--alt-text-add-image);
                  mask-image:var(--alt-text-add-image);
          -webkit-mask-repeat:no-repeat;
                  mask-repeat:no-repeat;
          -webkit-mask-position:center;
                  mask-position:center;
          display:inline-block;
          width:12px;
          height:13px;
          background-color:var(--editor-toolbar-fg-color);
          margin-inline-end:4px;
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText:hover::before{
          background-color:var(--editor-toolbar-hover-fg-color);
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText.done::before{
          -webkit-mask-image:var(--alt-text-done-image);
                  mask-image:var(--alt-text-done-image);
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText .tooltip{
          display:none;
        }

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText .tooltip.show{
            --alt-text-tooltip-bg:#f0f0f4;
            --alt-text-tooltip-fg:#15141a;
            --alt-text-tooltip-border:#8f8f9d;
            --alt-text-tooltip-shadow:0px 2px 6px 0px rgb(58 57 68 / 0.2);

            display:inline-flex;
            flex-direction:column;
            align-items:center;
            justify-content:center;
            position:absolute;
            top:calc(100% + 2px);
            inset-inline-start:0;
            padding-block:2px 3px;
            padding-inline:3px;
            max-width:300px;
            width:-moz-max-content;
            width:max-content;
            height:auto;
            font-size:12px;

            border:0.5px solid var(--alt-text-tooltip-border);
            background:var(--alt-text-tooltip-bg);
            box-shadow:var(--alt-text-tooltip-shadow);
            color:var(--alt-text-tooltip-fg);

            pointer-events:none;
          }

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) :is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText .tooltip.show{
              --alt-text-tooltip-bg:#1c1b22;
              --alt-text-tooltip-fg:#fbfbfe;
              --alt-text-tooltip-shadow:0px 2px 6px 0px #15141a;
          }
            }

:where(html.is-dark) :is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText .tooltip.show{
              --alt-text-tooltip-bg:#1c1b22;
              --alt-text-tooltip-fg:#fbfbfe;
              --alt-text-tooltip-shadow:0px 2px 6px 0px #15141a;
          }

@media screen and (forced-colors: active){

:is(.annotationEditorLayer
  :is(.freeTextEditor, .inkEditor, .stampEditor, .highlightEditor),.textLayer) .editToolbar .buttons .altText .tooltip.show{
              --alt-text-tooltip-bg:Canvas;
              --alt-text-tooltip-fg:CanvasText;
              --alt-text-tooltip-border:CanvasText;
              --alt-text-tooltip-shadow:none;
          }
            }

.annotationEditorLayer .freeTextEditor{
  padding:calc(var(--freetext-padding) * var(--scale-factor));
  width:auto;
  height:auto;
  touch-action:none;
}

.annotationEditorLayer .freeTextEditor .internal{
  background:transparent;
  border:none;
  inset:0;
  overflow:visible;
  white-space:nowrap;
  font:10px sans-serif;
  line-height:var(--freetext-line-height);
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
}

.annotationEditorLayer .freeTextEditor .overlay{
  position:absolute;
  display:none;
  background:transparent;
  inset:0;
  width:100%;
  height:100%;
}

.annotationEditorLayer freeTextEditor .overlay.enabled{
  display:block;
}

.annotationEditorLayer .freeTextEditor .internal:empty::before{
  content:attr(default-content);
  color:gray;
}

.annotationEditorLayer .freeTextEditor .internal:focus{
  outline:none;
  -webkit-user-select:auto;
     -moz-user-select:auto;
          user-select:auto;
}

.annotationEditorLayer .inkEditor{
  width:100%;
  height:100%;
}

.annotationEditorLayer .inkEditor.editing{
  cursor:inherit;
}

.annotationEditorLayer .inkEditor .inkEditorCanvas{
  position:absolute;
  inset:0;
  width:100%;
  height:100%;
  touch-action:none;
}

.annotationEditorLayer .stampEditor{
  width:auto;
  height:auto;
}

.annotationEditorLayer .stampEditor canvas{
  width:100%;
  height:100%;
}

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers{
      position:absolute;
      inset:0;
    }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers.hidden{
        display:none;
      }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer{
        width:var(--resizer-size);
        height:var(--resizer-size);
        background:content-box var(--resizer-bg-color);
        border:var(--focus-outline-around);
        border-radius:2px;
        position:absolute;
      }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.topLeft{
          top:var(--resizer-shift);
          left:var(--resizer-shift);
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.topMiddle{
          top:var(--resizer-shift);
          left:calc(50% + var(--resizer-shift));
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.topRight{
          top:var(--resizer-shift);
          right:var(--resizer-shift);
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.middleRight{
          top:calc(50% + var(--resizer-shift));
          right:var(--resizer-shift);
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.bottomRight{
          bottom:var(--resizer-shift);
          right:var(--resizer-shift);
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.bottomMiddle{
          bottom:var(--resizer-shift);
          left:calc(50% + var(--resizer-shift));
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.bottomLeft{
          bottom:var(--resizer-shift);
          left:var(--resizer-shift);
        }

.annotationEditorLayer :is(.freeTextEditor, .inkEditor, .stampEditor) > .resizers > .resizer.middleLeft{
          top:calc(50% + var(--resizer-shift));
          left:var(--resizer-shift);
        }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.bottomRight,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.bottomRight,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.bottomRight,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.bottomRight{
        cursor:nwse-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.bottomMiddle,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.bottomMiddle,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.bottomMiddle,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.bottomMiddle{
        cursor:ns-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.bottomLeft,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.bottomLeft,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.bottomLeft,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.bottomLeft{
        cursor:nesw-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.middleLeft,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.middleLeft,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.middleLeft,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.middleLeft{
        cursor:ew-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.topLeft,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.bottomRight,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.bottomRight,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.bottomRight,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.bottomRight{
        cursor:nesw-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.topMiddle,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.bottomMiddle,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.bottomMiddle,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.bottomMiddle,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.bottomMiddle{
        cursor:ew-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.topRight,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.bottomLeft,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.bottomLeft,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.bottomLeft,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.bottomLeft{
        cursor:nwse-resize;
      }

.annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.middleRight,
      .annotationEditorLayer[data-main-rotation="0"]
    :is([data-editor-rotation="90"], [data-editor-rotation="270"]) > .resizers > .resizer.middleLeft,
      .annotationEditorLayer[data-main-rotation="90"]
    :is([data-editor-rotation="0"], [data-editor-rotation="180"]) > .resizers > .resizer.middleLeft,
      .annotationEditorLayer[data-main-rotation="180"]
    :is([data-editor-rotation="270"], [data-editor-rotation="90"]) > .resizers > .resizer.middleLeft,
      .annotationEditorLayer[data-main-rotation="270"]
    :is([data-editor-rotation="180"], [data-editor-rotation="0"]) > .resizers > .resizer.middleLeft{
        cursor:ns-resize;
      }

.annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="90"],
      [data-main-rotation="90"] [data-editor-rotation="0"],
      [data-main-rotation="180"] [data-editor-rotation="270"],
      [data-main-rotation="270"] [data-editor-rotation="180"]
    ) .editToolbar{
      rotate:270deg;
    }

[dir="ltr"] .annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="90"],
      [data-main-rotation="90"] [data-editor-rotation="0"],
      [data-main-rotation="180"] [data-editor-rotation="270"],
      [data-main-rotation="270"] [data-editor-rotation="180"]
    ) .editToolbar{
        inset-inline-end:calc(0px - var(--editor-toolbar-vert-offset));
        inset-block-start:0;
      }

[dir="rtl"] .annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="90"],
      [data-main-rotation="90"] [data-editor-rotation="0"],
      [data-main-rotation="180"] [data-editor-rotation="270"],
      [data-main-rotation="270"] [data-editor-rotation="180"]
    ) .editToolbar{
        inset-inline-end:calc(100% + var(--editor-toolbar-vert-offset));
        inset-block-start:0;
      }

.annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="180"],
      [data-main-rotation="90"] [data-editor-rotation="90"],
      [data-main-rotation="180"] [data-editor-rotation="0"],
      [data-main-rotation="270"] [data-editor-rotation="270"]
    ) .editToolbar{
      rotate:180deg;
      inset-inline-end:100%;
      inset-block-start:calc(0pc - var(--editor-toolbar-vert-offset));
    }

.annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="270"],
      [data-main-rotation="90"] [data-editor-rotation="180"],
      [data-main-rotation="180"] [data-editor-rotation="90"],
      [data-main-rotation="270"] [data-editor-rotation="0"]
    ) .editToolbar{
      rotate:90deg;
    }

[dir="ltr"] .annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="270"],
      [data-main-rotation="90"] [data-editor-rotation="180"],
      [data-main-rotation="180"] [data-editor-rotation="90"],
      [data-main-rotation="270"] [data-editor-rotation="0"]
    ) .editToolbar{
        inset-inline-end:calc(100% + var(--editor-toolbar-vert-offset));
        inset-block-start:100%;
      }

[dir="rtl"] .annotationEditorLayer
    :is(
      [data-main-rotation="0"] [data-editor-rotation="270"],
      [data-main-rotation="90"] [data-editor-rotation="180"],
      [data-main-rotation="180"] [data-editor-rotation="90"],
      [data-main-rotation="270"] [data-editor-rotation="0"]
    ) .editToolbar{
        inset-inline-start:calc(0px - var(--editor-toolbar-vert-offset));
        inset-block-start:0;
      }

#altTextDialog{
  --dialog-bg-color:white;
  --dialog-border-color:white;
  --dialog-shadow:0 2px 14px 0 rgb(58 57 68 / 0.2);
  --text-primary-color:#15141a;
  --text-secondary-color:#5b5b66;
  --hover-filter:brightness(0.9);
  --focus-ring-color:#0060df;
  --focus-ring-outline:2px solid var(--focus-ring-color);

  --textarea-border-color:#8f8f9d;
  --textarea-bg-color:white;
  --textarea-fg-color:var(--text-secondary-color);

  --radio-bg-color:#f0f0f4;
  --radio-checked-bg-color:#fbfbfe;
  --radio-border-color:#8f8f9d;
  --radio-checked-border-color:#0060df;

  --button-cancel-bg-color:#f0f0f4;
  --button-cancel-fg-color:var(--text-primary-color);
  --button-cancel-border-color:var(--button-cancel-bg-color);
  --button-cancel-hover-bg-color:var(--button-cancel-bg-color);
  --button-cancel-hover-fg-color:var(--button-cancel-fg-color);
  --button-cancel-hover-border-color:var(--button-cancel-hover-bg-color);

  --button-save-bg-color:#0060df;
  --button-save-fg-color:#fbfbfe;
  --button-save-hover-bg-color:var(--button-save-bg-color);
  --button-save-hover-fg-color:var(--button-save-fg-color);
  --button-save-hover-border-color:var(--button-save-hover-bg-color);

  font:message-box;
  font-size:13px;
  font-weight:400;
  line-height:150%;
  border-radius:4px;
  padding:12px 16px;
  border:1px solid var(--dialog-border-color);
  background:var(--dialog-bg-color);
  color:var(--text-primary-color);
  box-shadow:var(--dialog-shadow);
}

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) #altTextDialog{
    --dialog-bg-color:#1c1b22;
    --dialog-border-color:#1c1b22;
    --dialog-shadow:0 2px 14px 0 #15141a;
    --text-primary-color:#fbfbfe;
    --text-secondary-color:#cfcfd8;
    --focus-ring-color:#0df;
    --hover-filter:brightness(1.4);

    --textarea-bg-color:#42414d;

    --radio-bg-color:#2b2a33;
    --radio-checked-bg-color:#15141a;
    --radio-checked-border-color:#0df;

    --button-cancel-bg-color:#2b2a33;
    --button-save-bg-color:#0df;
    --button-save-fg-color:#15141a;
}
  }

:where(html.is-dark) #altTextDialog{
    --dialog-bg-color:#1c1b22;
    --dialog-border-color:#1c1b22;
    --dialog-shadow:0 2px 14px 0 #15141a;
    --text-primary-color:#fbfbfe;
    --text-secondary-color:#cfcfd8;
    --focus-ring-color:#0df;
    --hover-filter:brightness(1.4);

    --textarea-bg-color:#42414d;

    --radio-bg-color:#2b2a33;
    --radio-checked-bg-color:#15141a;
    --radio-checked-border-color:#0df;

    --button-cancel-bg-color:#2b2a33;
    --button-save-bg-color:#0df;
    --button-save-fg-color:#15141a;
}

@media screen and (forced-colors: active){

#altTextDialog{
    --dialog-bg-color:Canvas;
    --dialog-border-color:CanvasText;
    --dialog-shadow:none;
    --text-primary-color:CanvasText;
    --text-secondary-color:CanvasText;
    --hover-filter:none;
    --focus-ring-color:ButtonBorder;

    --textarea-border-color:ButtonBorder;
    --textarea-bg-color:Field;
    --textarea-fg-color:ButtonText;

    --radio-bg-color:ButtonFace;
    --radio-checked-bg-color:ButtonFace;
    --radio-border-color:ButtonText;
    --radio-checked-border-color:ButtonText;

    --button-cancel-bg-color:ButtonFace;
    --button-cancel-fg-color:ButtonText;
    --button-cancel-border-color:ButtonText;
    --button-cancel-hover-bg-color:AccentColor;
    --button-cancel-hover-fg-color:AccentColorText;

    --button-save-bg-color:ButtonText;
    --button-save-fg-color:ButtonFace;
    --button-save-hover-bg-color:AccentColor;
    --button-save-hover-fg-color:AccentColorText;
}
  }

#altTextDialog::backdrop{
    -webkit-mask:url(#alttext-manager-mask);
            mask:url(#alttext-manager-mask);
  }

#altTextDialog.positioned{
    margin:0;
  }

#altTextDialog #altTextContainer{
    width:300px;
    height:-moz-fit-content;
    height:fit-content;
    display:inline-flex;
    flex-direction:column;
    align-items:flex-start;
    gap:16px;
  }

#altTextDialog #altTextContainer *:focus-visible{
      outline:var(--focus-ring-outline);
      outline-offset:2px;
    }

#altTextDialog #altTextContainer .radio{
      display:flex;
      flex-direction:column;
      align-items:flex-start;
      gap:4px;
    }

#altTextDialog #altTextContainer .radio .radioButton{
        display:flex;
        gap:8px;
        align-self:stretch;
        align-items:center;
      }

#altTextDialog #altTextContainer .radio .radioButton input{
          -webkit-appearance:none;
             -moz-appearance:none;
                  appearance:none;
          box-sizing:border-box;
          width:16px;
          height:16px;
          border-radius:50%;
          background-color:var(--radio-bg-color);
          border:1px solid var(--radio-border-color);
        }

#altTextDialog #altTextContainer .radio .radioButton input:hover{
            filter:var(--hover-filter);
          }

#altTextDialog #altTextContainer .radio .radioButton input:checked{
            background-color:var(--radio-checked-bg-color);
            border:4px solid var(--radio-checked-border-color);
          }

#altTextDialog #altTextContainer .radio .radioLabel{
        display:flex;
        padding-inline-start:24px;
        align-items:flex-start;
        gap:10px;
        align-self:stretch;
      }

#altTextDialog #altTextContainer .radio .radioLabel span{
          flex:1 0 0;
          font-size:11px;
          color:var(--text-secondary-color);
        }

#altTextDialog #altTextContainer #overallDescription{
      display:flex;
      flex-direction:column;
      align-items:flex-start;
      gap:4px;
      align-self:stretch;
    }

#altTextDialog #altTextContainer #overallDescription span{
        align-self:stretch;
      }

#altTextDialog #altTextContainer #overallDescription .title{
        font-size:13px;
        font-style:normal;
        font-weight:590;
      }

#altTextDialog #altTextContainer #addDescription{
      display:flex;
      flex-direction:column;
      align-items:stretch;
      gap:8px;
    }

#altTextDialog #altTextContainer #addDescription .descriptionArea{
        flex:1;
        padding-inline:24px 10px;
      }

#altTextDialog #altTextContainer #addDescription .descriptionArea textarea{
          font:inherit;
          width:100%;
          min-height:75px;
          padding:8px;
          resize:none;
          margin:0;
          box-sizing:border-box;
          border-radius:4px;
          border:1px solid var(--textarea-border-color);
          background:var(--textarea-bg-color);
          color:var(--textarea-fg-color);
        }

#altTextDialog #altTextContainer #addDescription .descriptionArea textarea:focus{
            outline-offset:0;
            border-color:transparent;
          }

#altTextDialog #altTextContainer #addDescription .descriptionArea textarea:disabled{
            pointer-events:none;
            opacity:0.4;
          }

#altTextDialog #altTextContainer #buttons{
      display:flex;
      justify-content:flex-end;
      align-items:flex-start;
      gap:8px;
      align-self:stretch;
    }

#altTextDialog #altTextContainer #buttons button{
        border-radius:4px;
        border:1px solid;
        font:menu;
        font-weight:600;
        padding:4px 16px;
        width:auto;
        height:32px;
      }

#altTextDialog #altTextContainer #buttons button:hover{
          cursor:pointer;
          filter:var(--hover-filter);
        }

#altTextDialog #altTextContainer #buttons button#altTextCancel{
          color:var(--button-cancel-fg-color);
          background-color:var(--button-cancel-bg-color);
          border-color:var(--button-cancel-border-color);
        }

#altTextDialog #altTextContainer #buttons button#altTextCancel:hover{
            color:var(--button-cancel-hover-fg-color);
            background-color:var(--button-cancel-hover-bg-color);
            border-color:var(--button-cancel-hover-border-color);
          }

#altTextDialog #altTextContainer #buttons button#altTextSave{
          color:var(--button-save-hover-fg-color);
          background-color:var(--button-save-hover-bg-color);
          border-color:var(--button-save-hover-border-color);
          opacity:1;
        }

#altTextDialog #altTextContainer #buttons button#altTextSave:hover{
            color:var(--button-save-hover-fg-color);
            background-color:var(--button-save-hover-bg-color);
            border-color:var(--button-save-hover-border-color);
          }

.colorPicker{
  --hover-outline-color:#0250bb;
  --selected-outline-color:#0060df;
  --swatch-border-color:#cfcfd8;
}

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) .colorPicker{
    --hover-outline-color:#80ebff;
    --selected-outline-color:#aaf2ff;
    --swatch-border-color:#52525e;
}
  }

:where(html.is-dark) .colorPicker{
    --hover-outline-color:#80ebff;
    --selected-outline-color:#aaf2ff;
    --swatch-border-color:#52525e;
}

@media screen and (forced-colors: active){

.colorPicker{
    --hover-outline-color:Highlight;
    --selected-outline-color:var(--hover-outline-color);
    --swatch-border-color:ButtonText;
}
  }

.colorPicker .swatch{
    width:16px;
    height:16px;
    border:1px solid var(--swatch-border-color);
    border-radius:100%;
    outline-offset:2px;
    box-sizing:border-box;
    forced-color-adjust:none;
  }

.colorPicker button:is(:hover, .selected) > .swatch{
    border:none;
  }

.annotationEditorLayer[data-main-rotation="0"] .highlightEditor:not(.free) > .editToolbar{
      rotate:0deg;
    }

.annotationEditorLayer[data-main-rotation="90"] .highlightEditor:not(.free) > .editToolbar{
      rotate:270deg;
    }

.annotationEditorLayer[data-main-rotation="180"] .highlightEditor:not(.free) > .editToolbar{
      rotate:180deg;
    }

.annotationEditorLayer[data-main-rotation="270"] .highlightEditor:not(.free) > .editToolbar{
      rotate:90deg;
    }

.annotationEditorLayer .highlightEditor{
    position:absolute;
    background:transparent;
    z-index:1;
    cursor:auto;
    max-width:100%;
    max-height:100%;
    border:none;
    outline:none;
    pointer-events:none;
    transform-origin:0 0;
  }

.annotationEditorLayer .highlightEditor:not(.free){
      transform:none;
    }

.annotationEditorLayer .highlightEditor .internal{
      position:absolute;
      top:0;
      left:0;
      width:100%;
      height:100%;
      pointer-events:auto;
    }

.annotationEditorLayer .highlightEditor.disabled .internal{
      pointer-events:none;
    }

.annotationEditorLayer .highlightEditor.selectedEditor .internal{
        cursor:pointer;
      }

.annotationEditorLayer .highlightEditor .editToolbar{
      --editor-toolbar-colorpicker-arrow-image:url(images/toolbarButton-menuArrow.svg);

      transform-origin:center !important;
    }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker{
          position:relative;
          width:auto;
          display:flex;
          justify-content:center;
          align-items:center;
          gap:4px;
          padding:4px;
        }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker::after{
            content:"";
            -webkit-mask-image:var(--editor-toolbar-colorpicker-arrow-image);
                    mask-image:var(--editor-toolbar-colorpicker-arrow-image);
            -webkit-mask-repeat:no-repeat;
                    mask-repeat:no-repeat;
            -webkit-mask-position:center;
                    mask-position:center;
            display:inline-block;
            background-color:var(--editor-toolbar-fg-color);
            width:12px;
            height:12px;
          }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker:hover::after{
            background-color:var(--editor-toolbar-hover-fg-color);
          }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker:has(.dropdown:not(.hidden)){
            background-color:var(--editor-toolbar-hover-bg-color);
          }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker:has(.dropdown:not(.hidden))::after{
              scale:-1;
            }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker .dropdown{
            position:absolute;
            display:flex;
            justify-content:center;
            align-items:center;
            flex-direction:column;
            gap:11px;
            padding-block:8px;
            border-radius:6px;
            background-color:var(--editor-toolbar-bg-color);
            border:1px solid var(--editor-toolbar-border-color);
            box-shadow:var(--editor-toolbar-shadow);
            inset-block-start:calc(100% + 4px);
            width:calc(100% + 2 * var(--editor-toolbar-padding));
          }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker .dropdown button{
              width:100%;
              height:auto;
              border:none;
              cursor:pointer;
              display:flex;
              justify-content:center;
              align-items:center;
              background:none;
            }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker .dropdown button:is(:active, :focus-visible){
                outline:none;
              }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker .dropdown button > .swatch{
                outline-offset:2px;
              }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker .dropdown button[aria-selected="true"] > .swatch{
                outline:2px solid var(--selected-outline-color);
              }

.annotationEditorLayer .highlightEditor .editToolbar .buttons .colorPicker .dropdown button:is(:hover, :active, :focus-visible) > .swatch{
                outline:2px solid var(--hover-outline-color);
              }

.editorParamsToolbar:has(#highlightParamsToolbarContainer){
  padding:unset;
}

#highlightParamsToolbarContainer{
  height:auto;
  padding-inline:10px;
  padding-block:10px 16px;
  gap:16px;
  display:flex;
  flex-direction:column;
  box-sizing:border-box;
}

#highlightParamsToolbarContainer .editorParamsLabel{
    width:-moz-fit-content;
    width:fit-content;
    inset-inline-start:0;
  }

#highlightParamsToolbarContainer .colorPicker{
    display:flex;
    flex-direction:column;
    gap:8px;
  }

#highlightParamsToolbarContainer .colorPicker .dropdown{
      display:flex;
      justify-content:space-between;
      align-items:center;
      flex-direction:row;
      height:auto;
    }

#highlightParamsToolbarContainer .colorPicker .dropdown button{
        width:auto;
        height:auto;
        border:none;
        cursor:pointer;
        display:flex;
        justify-content:center;
        align-items:center;
        background:none;
        flex:0 0 auto;
      }

#highlightParamsToolbarContainer .colorPicker .dropdown button .swatch{
          width:24px;
          height:24px;
        }

#highlightParamsToolbarContainer .colorPicker .dropdown button:is(:active, :focus-visible){
          outline:none;
        }

#highlightParamsToolbarContainer .colorPicker .dropdown button[aria-selected="true"] > .swatch{
          outline:2px solid var(--selected-outline-color);
        }

#highlightParamsToolbarContainer .colorPicker .dropdown button:is(:hover, :active, :focus-visible) > .swatch{
          outline:2px solid var(--hover-outline-color);
        }

#highlightParamsToolbarContainer #editorHighlightThickness{
    display:flex;
    flex-direction:column;
    align-items:center;
    gap:4px;
    align-self:stretch;
  }

#highlightParamsToolbarContainer #editorHighlightThickness .editorParamsLabel{
      width:100%;
      height:auto;
      align-self:stretch;
    }

#highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker{
      display:flex;
      justify-content:space-between;
      align-items:center;
      align-self:stretch;

      --example-color:#bfbfc9;
    }

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) #highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker{
        --example-color:#80808e;
    }
      }

:where(html.is-dark) #highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker{
        --example-color:#80808e;
    }

@media screen and (forced-colors: active){

#highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker{
        --example-color:CanvasText;
    }
      }

:is(#highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker > .editorParamsSlider[disabled]){
        opacity:0.4;
      }

#highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker::before,
      #highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker::after{
        content:"";
        width:8px;
        aspect-ratio:1;
        display:block;
        border-radius:100%;
        background-color:var(--example-color);
      }

#highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker::after{
        width:24px;
      }

#highlightParamsToolbarContainer #editorHighlightThickness .thicknessPicker .editorParamsSlider{
        width:unset;
        height:14px;
      }

#highlightParamsToolbarContainer #editorHighlightVisibility{
    display:flex;
    flex-direction:column;
    align-items:flex-start;
    gap:8px;
    align-self:stretch;
  }

#highlightParamsToolbarContainer #editorHighlightVisibility .divider{
      --divider-color:#d7d7db;

      margin-block:4px;
      width:100%;
      height:1px;
      background-color:var(--divider-color);
    }

@media (prefers-color-scheme: light){

:where(html:not(.is-light)) #highlightParamsToolbarContainer #editorHighlightVisibility .divider{
        --divider-color:#8f8f9d;
    }
      }

:where(html.is-dark) #highlightParamsToolbarContainer #editorHighlightVisibility .divider{
        --divider-color:#8f8f9d;
    }

@media screen and (forced-colors: active){

#highlightParamsToolbarContainer #editorHighlightVisibility .divider{
        --divider-color:CanvasText;
    }
      }

#highlightParamsToolbarContainer #editorHighlightVisibility .toggler{
      display:flex;
      justify-content:space-between;
      align-items:center;
      align-self:stretch;
    }

:root{
  --viewer-container-height:0;
  --pdfViewer-padding-bottom:0;
  --page-margin:1px auto -8px;
  --page-border:9px solid transparent;
  --spreadHorizontalWrapped-margin-LR:-3.5px;
  --loading-icon-delay:400ms;

  --num-page-width: 10px;
}

@media screen and (forced-colors: active){
  :root{
    --pdfViewer-padding-bottom:9px;
    --page-margin:8px auto -1px;
    --page-border:1px solid CanvasText;
    --spreadHorizontalWrapped-margin-LR:3.5px;
  }
}

[data-main-rotation="90"]{
  transform:rotate(90deg) translateY(-100%);
}
[data-main-rotation="180"]{
  transform:rotate(180deg) translate(-100%, -100%);
}
[data-main-rotation="270"]{
  transform:rotate(270deg) translateX(-100%);
}

#hiddenCopyElement,
.hiddenCanvasElement{
  position:absolute;
  top:0;
  left:0;
  width:0;
  height:0;
  display:none;
}

.pdfViewer{
  --scale-factor:1;

  padding-bottom:var(--pdfViewer-padding-bottom);

  --hcm-highlight-filter:none;
  --hcm-highlight-selected-filter:none;
}

@media screen and (forced-colors: active){

.pdfViewer{
    --hcm-highlight-filter:invert(100%);
}
  }

.pdfViewer .canvasWrapper{
  overflow:hidden;
  width:100%;
  height:100%;
}

.pdfViewer .page{
  direction:ltr;
  width:816px;
  height:1056px;
  margin:var(--page-margin);
  position:relative;
  overflow:visible;
  border:var(--page-border);
  background-clip:content-box;
  background-color:rgb(255 255 255);
}

.pdfViewer .dummyPage{
  position:relative;
  width:0;
  height:var(--viewer-container-height);
}

.pdfViewer.noUserSelect{
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
}

.pdfViewer.removePageBorders .page{
  margin:0 auto 10px;
  border:none;
}

.pdfViewer:is(.scrollHorizontal, .scrollWrapped),
.spread{
  margin-inline:3.5px;
  text-align:center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pdfViewer.scrollHorizontal,
.spread{
  white-space:nowrap;
}

.pdfViewer.removePageBorders,
.pdfViewer:is(.scrollHorizontal, .scrollWrapped) .spread{
  margin-inline:0;
}

.spread :is(.page, .dummyPage),
.pdfViewer:is(.scrollHorizontal, .scrollWrapped) :is(.page, .spread){
  display:inline-block;
  vertical-align:middle;
}

.spread .page,
.pdfViewer:is(.scrollHorizontal, .scrollWrapped) .page{
  margin-inline:var(--spreadHorizontalWrapped-margin-LR);
}

.pdfViewer.removePageBorders .spread .page,
.pdfViewer.removePageBorders:is(.scrollHorizontal, .scrollWrapped) .page{
  margin-inline:5px;
}

.pdfViewer .page canvas{
  margin:0;
  display:block;
}

.pdfViewer .page canvas .structTree{
  contain:strict;
}

.pdfViewer .page canvas[hidden]{
  display:none;
}

.pdfViewer .page canvas[zooming]{
  width:100%;
  height:100%;
}

.pdfViewer .page.loadingIcon::after{
  position:absolute;
  top:0;
  left:0;
  content:"";
  width:100%;
  height:100%;
  background:url("images/loading-icon.gif") center no-repeat;
  display:none;
  transition-property:display;
  transition-delay:var(--loading-icon-delay);
  z-index:5;
  contain:strict;
}

.pdfViewer .page.loading::after{
  display:block;
}

.pdfViewer .page:not(.loading)::after{
  transition-property:none;
  display:none;
}

.pdfPresentationMode .pdfViewer{
  padding-bottom:0;
}

.pdfPresentationMode .spread{
  margin:0;
}

.pdfPresentationMode .pdfViewer .page{
  margin:0 auto;
  border:2px solid transparent;
}

:root{

  --toolbar-height: 48px;

  --dir-factor:1;
  --inline-start:left;
  --inline-end:right;

  --sidebar-width:128px;
  --sidebar-transition-duration:200ms;
  --sidebar-transition-timing-function:ease;

  --toolbar-icon-opacity:0.7;
  --doorhanger-icon-opacity:0.9;
  --editor-toolbar-base-offset:105px;

  --main-color:rgb(12 12 13);
  --body-bg-color:rgb(255, 255, 255);
  --progressBar-color:rgb(10 132 255);
  --progressBar-bg-color:rgb(221 221 222);
  --progressBar-blend-color:rgb(116 177 239);
  --scrollbar-color:auto;
  --scrollbar-bg-color:auto;
  --toolbar-icon-bg-color:rgb(0 0 0);
  --toolbar-icon-hover-bg-color:rgb(0 0 0);

  --sidebar-narrow-bg-color:rgb(212 212 215 / 0.9);
  --sidebar-toolbar-bg-color:rgb(245 246 247);
  --toolbar-bg-color:rgb(255, 255, 255);
  --toolbar-border-color:#EBECF0;
  --toolbar-box-shadow:0 1px 0 var(--toolbar-border-color);
  --toolbar-border-bottom:none;
  --toolbarSidebar-box-shadow:inset calc(-1px * var(--dir-factor)) 0 0 rgb(0 0 0 / 0.25), 0 1px 0 rgb(0 0 0 / 0.15), 0 0 1px rgb(0 0 0 / 0.1);
  --toolbarSidebar-border-bottom:none;
  --button-hover-color: #F4F5F9;
  --toggled-btn-color:rgb(0 0 0);
  --toggled-btn-bg-color:rgb(0 0 0 / 0.3);
  --toggled-hover-active-btn-color:rgb(0 0 0 / 0.4);
  --toggled-hover-btn-outline:none;
  --dropdown-btn-bg-color:rgb(215 215 219);
  --dropdown-btn-border:none;
  --separator-color:rgb(0 0 0 / 0.3);
  --field-color:rgb(6 6 6);
  --field-bg-color:rgb(255 255 255);
  --field-border-color:rgb(187 187 188);
  --treeitem-color:rgb(0 0 0 / 0.8);
  --treeitem-bg-color:rgb(0 0 0 / 0.15);
  --treeitem-hover-color:rgb(0 0 0 / 0.9);
  --treeitem-selected-color:rgb(0 0 0 / 0.9);
  --treeitem-selected-bg-color:rgb(0 0 0 / 0.25);
  --thumbnail-hover-color: #0D53DE;
  --thumbnail-selected-color: #0D53DE;
  --doorhanger-bg-color:rgb(255 255 255);
  --doorhanger-border-color:rgb(12 12 13 / 0.2);
  --doorhanger-hover-color:rgb(12 12 13);
  --doorhanger-hover-bg-color:rgb(237 237 237);
  --doorhanger-separator-color:rgb(222 222 222);
  --dialog-button-border:none;
  --dialog-button-bg-color:rgb(12 12 13 / 0.1);
  --dialog-button-hover-bg-color:rgb(12 12 13 / 0.3);

  --loading-icon:url(images/loading.svg);
  --treeitem-expanded-icon:url(images/treeitem-expanded.svg);
  --treeitem-collapsed-icon:url(images/treeitem-collapsed.svg);
  --toolbarButton-editorFreeText-icon:url(images/toolbarButton-editorFreeText.svg);
  --toolbarButton-editorHighlight-icon:url(images/toolbarButton-editorHighlight.svg);
  --toolbarButton-editorInk-icon:url(images/toolbarButton-editorInk.svg);
  --toolbarButton-editorStamp-icon:url(images/toolbarButton-editorStamp.svg);
  --toolbarButton-menuArrow-icon:url(images/toolbarButton-menuArrow.svg);
  --toolbarButton-sidebarToggle-icon:url(images/toolbarButton-sidebarToggle.svg);
  --toolbarButton-sidebarToggle-icon-open:url(images/toolbarButton-sidebarToggle-open.svg);
  --toolbarButton-secondaryToolbarToggle-icon:url(images/toolbarButton-secondaryToolbarToggle.svg);
  --toolbarButton-pageUp-icon:url(images/toolbarButton-pageUp.svg);
  --toolbarButton-pageDown-icon:url(images/toolbarButton-pageDown.svg);
  --toolbarButton-layerChange-icon:url(images/layer-button.svg);
  --toolbarButton-layerChange-icon-show:url(images/layer-button-show.svg);
  --toolbarButton-zoomOut-icon:url(images/toolbarButton-zoomOut.svg);
  --toolbarButton-zoomIn-icon:url(images/toolbarButton-zoomIn.svg);
  --toolbarButton-presentationMode-icon:url(images/toolbarButton-presentationMode.svg);
  --toolbarButton-print-icon:url(images/toolbarButton-print.svg);
  --toolbarButton-openFile-icon:url(images/toolbarButton-openFile.svg);
  --toolbarButton-download-icon:url(images/toolbarButton-download.svg);
  --toolbarButton-bookmark-icon:url(images/toolbarButton-bookmark.svg);
  --toolbarButton-viewThumbnail-icon:url(images/toolbarButton-viewThumbnail.svg);
  --toolbarButton-viewOutline-icon:url(images/toolbarButton-viewOutline.svg);
  --toolbarButton-viewAttachments-icon:url(images/toolbarButton-viewAttachments.svg);
  --toolbarButton-viewLayers-icon:url(images/toolbarButton-viewLayers.svg);
  --toolbarButton-currentOutlineItem-icon:url(images/toolbarButton-currentOutlineItem.svg);
  --toolbarButton-search-icon:url(images/toolbarButton-search.svg);
  --findbarButton-previous-icon:url(images/findbarButton-previous.svg);
  --findbarButton-next-icon:url(images/findbarButton-next.svg);
  --secondaryToolbarButton-firstPage-icon:url(images/secondaryToolbarButton-firstPage.svg);
  --secondaryToolbarButton-lastPage-icon:url(images/secondaryToolbarButton-lastPage.svg);
  --secondaryToolbarButton-rotateCcw-icon:url(images/secondaryToolbarButton-rotateCcw.svg);
  --secondaryToolbarButton-rotateCw-icon:url(images/secondaryToolbarButton-rotateCw.svg);
  --secondaryToolbarButton-selectTool-icon:url(images/secondaryToolbarButton-selectTool.svg);
  --secondaryToolbarButton-handTool-icon:url(images/secondaryToolbarButton-handTool.svg);
  --secondaryToolbarButton-scrollPage-icon:url(images/secondaryToolbarButton-scrollPage.svg);
  --secondaryToolbarButton-scrollVertical-icon:url(images/secondaryToolbarButton-scrollVertical.svg);
  --secondaryToolbarButton-scrollHorizontal-icon:url(images/secondaryToolbarButton-scrollHorizontal.svg);
  --secondaryToolbarButton-scrollWrapped-icon:url(images/secondaryToolbarButton-scrollWrapped.svg);
  --secondaryToolbarButton-spreadNone-icon:url(images/secondaryToolbarButton-spreadNone.svg);
  --secondaryToolbarButton-spreadOdd-icon:url(images/secondaryToolbarButton-spreadOdd.svg);
  --secondaryToolbarButton-spreadEven-icon:url(images/secondaryToolbarButton-spreadEven.svg);
  --secondaryToolbarButton-documentProperties-icon:url(images/secondaryToolbarButton-documentProperties.svg);
  --editorParams-stampAddImage-icon:url(images/toolbarButton-zoomIn.svg);
}

[dir="rtl"]:root{
  --dir-factor:-1;
  --inline-start:right;
  --inline-end:left;
}

@media (prefers-color-scheme: dark){
  :root:where(:not(.is-dark)){
    // custom ban dark theme
    /* --main-color:rgb(249 249 250);
    --body-bg-color:rgb(42 42 46);
    --progressBar-color:rgb(0 96 223);
    --progressBar-bg-color:rgb(40 40 43);
    --progressBar-blend-color:rgb(20 68 133);
    --scrollbar-color:rgb(121 121 123);
    --scrollbar-bg-color:rgb(35 35 39);
    --toolbar-icon-bg-color:rgb(255 255 255);
    --toolbar-icon-hover-bg-color:rgb(255 255 255);

    --sidebar-narrow-bg-color:rgb(42 42 46 / 0.9);
    --sidebar-toolbar-bg-color:rgb(50 50 52);
    --toolbar-bg-color:rgb(56 56 61);
    --toolbar-border-color:rgb(12 12 13);
    --button-hover-color: #F4F5F9;
    --toggled-btn-color:rgb(255 255 255);
    --toggled-btn-bg-color:rgb(0 0 0 / 0.3);
    --toggled-hover-active-btn-color:rgb(0 0 0 / 0.4);
    --dropdown-btn-bg-color:rgb(74 74 79);
    --separator-color:rgb(0 0 0 / 0.3);
    --field-color:rgb(250 250 250);
    --field-bg-color:rgb(64 64 68);
    --field-border-color:rgb(115 115 115);
    --treeitem-color:rgb(255 255 255 / 0.8);
    --treeitem-bg-color:rgb(255 255 255 / 0.15);
    --treeitem-hover-color:rgb(255 255 255 / 0.9);
    --treeitem-selected-color:rgb(255 255 255 / 0.9);
    --treeitem-selected-bg-color:rgb(255 255 255 / 0.25);
    --thumbnail-hover-color:rgb(255 255 255 / 0.1);
    --thumbnail-selected-color:rgb(255 255 255 / 0.2);
    --doorhanger-bg-color:rgb(74 74 79);
    --doorhanger-border-color:rgb(39 39 43);
    --doorhanger-hover-color:rgb(249 249 250);
    --doorhanger-hover-bg-color:rgb(93 94 98);
    --doorhanger-separator-color:rgb(92 92 97);
    --dialog-button-bg-color:rgb(92 92 97);
    --dialog-button-hover-bg-color:rgb(115 115 115); */
  }
}

:root:where(.is-dark){
    --main-color:rgb(249 249 250);
    --body-bg-color:rgb(42 42 46);
    --progressBar-color:rgb(0 96 223);
    --progressBar-bg-color:rgb(40 40 43);
    --progressBar-blend-color:rgb(20 68 133);
    --scrollbar-color:rgb(121 121 123);
    --scrollbar-bg-color:rgb(35 35 39);
    --toolbar-icon-bg-color:rgb(255 255 255);
    --toolbar-icon-hover-bg-color:rgb(255 255 255);

    --sidebar-narrow-bg-color:rgb(42 42 46 / 0.9);
    --sidebar-toolbar-bg-color:rgb(50 50 52);
    --toolbar-bg-color:rgb(56 56 61);
    --toolbar-border-color:rgb(12 12 13);
    --button-hover-color: #F4F5F9;
    --toggled-btn-color:rgb(255 255 255);
    --toggled-btn-bg-color:rgb(0 0 0 / 0.3);
    --toggled-hover-active-btn-color:rgb(0 0 0 / 0.4);
    --dropdown-btn-bg-color:rgb(74 74 79);
    --separator-color:rgb(0 0 0 / 0.3);
    --field-color:rgb(250 250 250);
    --field-bg-color:rgb(64 64 68);
    --field-border-color:rgb(115 115 115);
    --treeitem-color:rgb(255 255 255 / 0.8);
    --treeitem-bg-color:rgb(255 255 255 / 0.15);
    --treeitem-hover-color:rgb(255 255 255 / 0.9);
    --treeitem-selected-color:rgb(255 255 255 / 0.9);
    --treeitem-selected-bg-color:rgb(255 255 255 / 0.25);
    --thumbnail-hover-color:rgb(255 255 255 / 0.1);
    --thumbnail-selected-color:rgb(255 255 255 / 0.2);
    --doorhanger-bg-color:rgb(74 74 79);
    --doorhanger-border-color:rgb(39 39 43);
    --doorhanger-hover-color:rgb(249 249 250);
    --doorhanger-hover-bg-color:rgb(93 94 98);
    --doorhanger-separator-color:rgb(92 92 97);
    --dialog-button-bg-color:rgb(92 92 97);
    --dialog-button-hover-bg-color:rgb(115 115 115);
  }

@media screen and (forced-colors: active){
  :root{
    --button-hover-color:#F4F5F9;
    --doorhanger-hover-bg-color:Highlight;
    --toolbar-icon-opacity:1;
    --toolbar-icon-bg-color:ButtonText;
    --toolbar-icon-hover-bg-color:ButtonFace;
    --toggled-hover-active-btn-color:ButtonText;
    --toggled-hover-btn-outline:2px solid ButtonBorder;
    --toolbar-border-color:CanvasText;
    --toolbar-border-bottom:1px solid var(--toolbar-border-color);
    --toolbar-box-shadow:none;
    --toggled-btn-color:HighlightText;
    --toggled-btn-bg-color:LinkText;
    --doorhanger-hover-color:ButtonFace;
    --doorhanger-border-color-whcm:1px solid ButtonText;
    --doorhanger-triangle-opacity-whcm:0;
    --dialog-button-border:1px solid Highlight;
    --dialog-button-hover-bg-color:Highlight;
    --dialog-button-hover-color:ButtonFace;
    --dropdown-btn-border:1px solid ButtonText;
    --field-border-color:ButtonText;
    --main-color:CanvasText;
    --separator-color:GrayText;
    --doorhanger-separator-color:GrayText;
    --toolbarSidebar-box-shadow:none;
    --toolbarSidebar-border-bottom:1px solid var(--toolbar-border-color);
  }
}

@media screen and (prefers-reduced-motion: reduce){
  :root{
    --sidebar-transition-duration:0;
  }
}

*{
  padding:0;
  margin:0;
}

html,
body{
  height:100%;
  width:100%;
}

body{
  background-color:var(--body-bg-color);
  scrollbar-color:var(--scrollbar-color) var(--scrollbar-bg-color);
}

.hidden,
[hidden]{
  display:none !important;
}

#viewerContainer.pdfPresentationMode:fullscreen{
  top:0;
  background-color:rgb(0 0 0);
  width:100%;
  height:100%;
  overflow:hidden;
  cursor:none;
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
}

.pdfPresentationMode:fullscreen section:not([data-internal-link]){
  pointer-events:none;
}

.pdfPresentationMode:fullscreen .textLayer span{
  cursor:none;
}

.pdfPresentationMode.pdfPresentationModeControls > *,
.pdfPresentationMode.pdfPresentationModeControls .textLayer span{
  cursor:default;
}

#outerContainer{
  width:100%;
  height:100%;
  position:relative;
}

#sidebarContainer{
  position:absolute;
  inset-block:var(--toolbar-height) 0;
  inset-inline-start:calc(-1 * var(--sidebar-width));
  width:var(--sidebar-width);
  visibility:hidden;
  z-index:100;
  font:message-box;
  border-top:1px solid rgb(51 51 51);
  border-inline-end:var(--doorhanger-border-color-whcm);
  transition-property:inset-inline-start;
  transition-duration:var(--sidebar-transition-duration);
  transition-timing-function:var(--sidebar-transition-timing-function);
}

#outerContainer:is(.sidebarMoving, .sidebarOpen) #sidebarContainer{
  visibility:visible;
}
#outerContainer.sidebarOpen #sidebarContainer{
  inset-inline-start:0;
}

#mainContainer{
  position:absolute;
  inset:0;
  min-width:350px;
}

#sidebarContent{
  inset-block:0;
  inset-inline-start:0;
  overflow:auto;
  position:absolute;
  width:100%;
  border-right: 1px solid #EBECF0;
}

#viewerContainer{
  overflow:auto;
  position:absolute;
  inset:var(--toolbar-height) 0 0;
  outline:none;
  &::-webkit-scrollbar {
    scrollbar-gutter: stable;
    background: white !important;
    width: 6px !important;
    height: 6px !important;
  }

  /* 滚动条里面轨道 */
  &::-webkit-scrollbar-track {
    scrollbar-gutter: stable;
    background: white !important;
  }

  /* 滑块 */
  &::-webkit-scrollbar-thumb {
    background-color: #EBECF0 !important;
    scrollbar-gutter: stable;
    border-radius: 10px;
  }
}
#viewerContainer:not(.pdfPresentationMode){
  transition-duration:var(--sidebar-transition-duration);
  transition-timing-function:var(--sidebar-transition-timing-function);
}

#outerContainer.sidebarOpen #viewerContainer:not(.pdfPresentationMode){
  inset-inline-start:var(--sidebar-width);
  transition-property:inset-inline-start;
}

.toolbar{
  position:relative;
  inset-inline:0;
  z-index:9999;
  cursor:default;
  font:message-box;
}

:is(.toolbar, .editorParamsToolbar, .findbar, #sidebarContainer)
  :is(input, button, select),
.secondaryToolbar :is(input, button, a, select){
  outline:none;
  font:message-box;
}

#toolbarContainer{
  display: flex;
  width:100%;
}

#toolbarSidebar{
  width:100%;
  height:32px;
  background-color:var(--sidebar-toolbar-bg-color);
  box-shadow:var(--toolbarSidebar-box-shadow);
  border-bottom:var(--toolbarSidebar-border-bottom);
}

#sidebarResizer{
  position:absolute;
  inset-block:0;
  inset-inline-end:-6px;
  width:6px;
  z-index:200;
  cursor:ew-resize;
}

#toolbarContainer,
.findbar,
.secondaryToolbar,
.editorParamsToolbar{
  position:relative;
  height: var(--toolbar-height);
  background-color:var(--toolbar-bg-color);
  box-shadow:var(--toolbar-box-shadow);
  border-bottom:var(--toolbar-border-bottom);
}

#toolbarViewer{
  height: var(--toolbar-height);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

#loadingBar{
  --progressBar-percent:0%;
  --progressBar-end-offset:0;

  position:absolute;
  inset-inline:0 var(--progressBar-end-offset);
  height:4px;
  bottom: 0;
  transform: translateY(100%);
  background-color:var(--progressBar-bg-color);
  border-bottom:0px solid var(--toolbar-border-color);
  transition-property:inset-inline-start;
  transition-duration:var(--sidebar-transition-duration);
  transition-timing-function:var(--sidebar-transition-timing-function);
}

#outerContainer.sidebarOpen #loadingBar{
  inset-inline-start:var(--sidebar-width);
}

#loadingBar .progress{
  position:absolute;
  top:0;
  inset-inline-start:0;
  width:100%;
  transform:scaleX(var(--progressBar-percent));
  transform-origin:calc(50% - 50% * var(--dir-factor)) 0;
  height:100%;
  background-color:var(--progressBar-color);
  overflow:hidden;
  transition:transform 200ms;
}

@keyframes progressIndeterminate{
  0%{
    transform:translateX(calc(-142px * var(--dir-factor)));
  }
  100%{
    transform:translateX(0);
  }
}

#loadingBar.indeterminate .progress{
  transform:none;
  background-color:var(--progressBar-bg-color);
  transition:none;
}

#loadingBar.indeterminate .progress .glimmer{
  position:absolute;
  top:0;
  inset-inline-start:0;
  height:100%;
  width:calc(100% + 150px);
  background:repeating-linear-gradient(
    135deg,
    var(--progressBar-blend-color) 0,
    var(--progressBar-bg-color) 5px,
    var(--progressBar-bg-color) 45px,
    var(--progressBar-color) 55px,
    var(--progressBar-color) 95px,
    var(--progressBar-blend-color) 100px
  );
  animation:progressIndeterminate 1s linear infinite;
}

#outerContainer.sidebarResizing
  :is(#sidebarContainer, #viewerContainer, #loadingBar){
  transition-duration:0s;
}

.findbar,
.secondaryToolbar,
.editorParamsToolbar{
  top:32px;
  position:absolute;
  z-index:30000;
  height:auto;
  padding:0 4px;
  margin:4px 2px;
  font:message-box;
  font-size:12px;
  line-height:14px;
  text-align:left;
  cursor:default;
}

.findbar{
  inset-inline-start:64px;
  min-width:300px;
  background-color:var(--toolbar-bg-color);
}
.findbar > div{
  height:32px;
}
.findbar > div#findbarInputContainer{
  margin-inline-end:4px;
}
.findbar.wrapContainers > div,
.findbar.wrapContainers > div#findbarMessageContainer > *{
  clear:both;
}
.findbar.wrapContainers > div#findbarMessageContainer{
  height:auto;
}

.findbar input[type="checkbox"]{
  pointer-events:none;
}

.findbar label{
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
}

.findbar label:hover,
.findbar input:focus-visible + label{
  color:var(--toggled-btn-color);
  background-color:var(--button-hover-color);
}

.findbar .toolbarField[type="checkbox"]:checked + .toolbarLabel{
  background-color:var(--toggled-btn-bg-color) !important;
  color:var(--toggled-btn-color);
}

#findInput{
  width:200px;
}

#findInput::-moz-placeholder{
    font-style:normal;
  }

#findInput::placeholder{
    font-style:normal;
  }

.loadingInput:has(> #findInput[data-status="pending"])::after{
    display:block;
    visibility:visible;
  }

#findInput[data-status="notFound"]{
    background-color:rgb(255 102 102);
  }

.secondaryToolbar,
.editorParamsToolbar{
  padding:6px 0 10px;
  inset-inline-end:4px;
  height:auto;
  background-color:var(--doorhanger-bg-color);
}

.editorParamsToolbarContainer{
  width:220px;
  margin-bottom:-4px;
}

.editorParamsToolbarContainer > .editorParamsSetter{
  min-height:26px;
  display:flex;
  align-items:center;
  justify-content:space-between;
  padding-inline:10px;
}

.editorParamsToolbarContainer .editorParamsLabel{
  padding-inline-end:10px;
  flex:none;
  font:menu;
  font-size:13px;
  font-style:normal;
  font-weight:400;
  line-height:150%;
  color:var(--main-color);
}

.editorParamsToolbarContainer .editorParamsColor{
  width:32px;
  height:32px;
  flex:none;
}

.editorParamsToolbarContainer .editorParamsSlider{
  background-color:transparent;
  width:90px;
  flex:0 1 0;
}

.editorParamsToolbarContainer .editorParamsSlider::-moz-range-progress{
  background-color:black;
}

.editorParamsToolbarContainer .editorParamsSlider::-webkit-slider-runnable-track,
.editorParamsToolbarContainer .editorParamsSlider::-moz-range-track{
  background-color:black;
}

.editorParamsToolbarContainer .editorParamsSlider::-webkit-slider-thumb,
.editorParamsToolbarContainer .editorParamsSlider::-moz-range-thumb{
  background-color:white;
}

#secondaryToolbarButtonContainer{
  max-width:220px;
  min-height:26px;
  max-height:calc(var(--viewer-container-height) - 40px);
  overflow-y:auto;
  margin-bottom:-4px;
}

#editorStampParamsToolbar{
  inset-inline-end:calc(var(--editor-toolbar-base-offset) + 0px);
}

#editorInkParamsToolbar{
  inset-inline-end:calc(var(--editor-toolbar-base-offset) + 28px);
}

#editorFreeTextParamsToolbar{
  inset-inline-end:calc(var(--editor-toolbar-base-offset) + 56px);
}

#editorHighlightParamsToolbar{
  inset-inline-end:calc(var(--editor-toolbar-base-offset) + 84px);
}

#editorStampAddImage::before{
  -webkit-mask-image:var(--editorParams-stampAddImage-icon);
          mask-image:var(--editorParams-stampAddImage-icon);
}

.doorHanger,
.doorHangerRight{
  border-radius:2px;
  box-shadow:0 1px 5px var(--doorhanger-border-color), 0 0 0 1px var(--doorhanger-border-color);
  border:var(--doorhanger-border-color-whcm);
}
:is(.doorHanger, .doorHangerRight)::after,
:is(.doorHanger, .doorHangerRight)::before{
  bottom:100%;
  border:8px solid rgb(0 0 0 / 0);
  content:" ";
  height:0;
  width:0;
  position:absolute;
  pointer-events:none;
  opacity:var(--doorhanger-triangle-opacity-whcm);
}
.doorHanger::after{
  inset-inline-start:10px;
  margin-inline-start:-8px;
  border-bottom-color:var(--toolbar-bg-color);
}
.doorHangerRight::after{
  inset-inline-end:10px;
  margin-inline-end:-8px;
  border-bottom-color:var(--doorhanger-bg-color);
}
:is(.doorHanger, .doorHangerRight)::before{
  border-bottom-color:var(--doorhanger-border-color);
  border-width:9px;
}
.doorHanger::before{
  inset-inline-start:10px;
  margin-inline-start:-9px;
}
.doorHangerRight::before{
  inset-inline-end:10px;
  margin-inline-end:-9px;
}

#findResultsCount{
  background-color:rgb(217 217 217);
  color:rgb(82 82 82);
  text-align:center;
  padding:4px 5px;
  margin:5px;
}

#findMsg[data-status="notFound"]{
  font-weight:bold;
}

:is(#findResultsCount, #findMsg):empty{
  display:none;
}

#toolbarViewerMiddle{
  margin-left:auto;
  left:auto;
  margin-right:16px;
}

#toolbarViewerLeft,
#toolbarSidebarLeft{
  float:var(--inline-start);
}
#toolbarViewerRight,
#toolbarSidebarRight{
  float:var(--inline-end);
}

#toolbarViewerLeft > *,
#toolbarViewerMiddle > *,
#toolbarViewerRight > *,
#toolbarSidebarLeft *,
#toolbarSidebarRight *,
.findbar *{
  position:relative;
  float:var(--inline-start);
}

#toolbarViewerLeft{
  padding-inline-start:1px;
  width: calc(50% - 120px);
}
#toolbarViewerRight{
  padding-inline-end:1px;
  z-index: 99;
  position: absolute;
  right: 0;

}
#toolbarSidebarRight{
  padding-inline-end:2px;
}

.splitToolbarButton{
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.odlPageControl {
  position: absolute;
  left: calc(50% - 14px);
  transform: translateX(-50%);
}

.splitToolbarButton > .toolbarButton{
  cursor: pointer;
  float:var(--inline-start);
}

.toolbarButton,
.secondaryToolbarButton,
.dialogButton{
  border:none;
  background:none;
  width:16px;
  height: 16px;
  outline:none;
}

.dialogButton:is(:hover, :focus-visible){
  background-color:var(--dialog-button-hover-bg-color);
}

.dialogButton:is(:hover, :focus-visible) > span{
  color:var(--dialog-button-hover-color);
}

.toolbarButton > span{
  display:inline-block;
  width:0;
  height:0;
  overflow:hidden;
}

:is(.toolbarButton, .secondaryToolbarButton, .dialogButton)[disabled]{
  opacity:0.5;
}

.splitToolbarButton > .toolbarButton:is(:hover, :focus-visible),
.dropdownToolbarButton:hover{
  background-color:var(--button-hover-color);
}
.splitToolbarButton > .toolbarButton{
  position:relative;
  margin:0;
}
#toolbarSidebar .splitToolbarButton > .toolbarButton{
  margin-inline-end:2px;
}

.splitToolbarButtonSeparator{
  float:var(--inline-start);
  margin:4px 0;
  width:1px;
  height:20px;
  background-color:var(--separator-color);
}

.toolbarButton,
.dropdownToolbarButton,
.secondaryToolbarButton,
.dialogButton{
  min-width:16px;
  margin:2px 1px;
  border:none;
  border-radius:2px;
  color:var(--main-color);
  font-size:12px;
  line-height:14px;
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
  cursor:default;
  box-sizing:border-box;
}

.toolbarButton {
  min-width:12px;
}

.toolbarButton:is(:hover, :focus-visible){
  background-color:var(--button-hover-color);
}
.secondaryToolbarButton:is(:hover, :focus-visible){
  background-color:var(--doorhanger-hover-bg-color);
  color:var(--doorhanger-hover-color);
}

:is(.toolbarButton, .secondaryToolbarButton).toggled,
.splitToolbarButton.toggled > .toolbarButton.toggled{
  background-color:var(--toggled-btn-bg-color);
  color:var(--toggled-btn-color);
}

:is(.toolbarButton, .secondaryToolbarButton).toggled:hover,
.splitToolbarButton.toggled > .toolbarButton.toggled:hover{
  outline:var(--toggled-hover-btn-outline) !important;
}

:is(.toolbarButton, .secondaryToolbarButton).toggled::before{
  background-color:var(--toggled-btn-color);
}

:is(.toolbarButton, .secondaryToolbarButton).toggled:hover:active,
.splitToolbarButton.toggled > .toolbarButton.toggled:hover:active{
  background-color:var(--toggled-hover-active-btn-color);
}

.dropdownToolbarButton{
  display:flex;
  width:-moz-fit-content;
  width:fit-content;
  min-width:140px;
  padding:0;
  background-color:var(--dropdown-btn-bg-color);
  border:var(--dropdown-btn-border);
}
.dropdownToolbarButton::after{
  top:6px;
  inset-inline-end:6px;
  pointer-events:none;
  -webkit-mask-image:var(--toolbarButton-menuArrow-icon);
          mask-image:var(--toolbarButton-menuArrow-icon);
}

.dropdownToolbarButton > select{
  -webkit-appearance:none;
     -moz-appearance:none;
          appearance:none;
  width:inherit;
  min-width:inherit;
  height:28px;
  font-size:12px;
  color:var(--main-color);
  margin:0;
  padding-block:1px 2px;
  padding-inline:6px 38px;
  border:none;
  background-color:var(--dropdown-btn-bg-color);
}
.dropdownToolbarButton > select:is(:hover, :focus-visible){
  background-color:var(--button-hover-color);
  color:var(--toggled-btn-color);
}
.dropdownToolbarButton > select > option{
  background:var(--doorhanger-bg-color);
  color:var(--main-color);
}

.toolbarButtonSpacer{
  width:30px;
  display:inline-block;
  height:1px;
}

:is(.toolbarButton, .secondaryToolbarButton, .treeItemToggler)::before,
.dropdownToolbarButton::after{
  position:absolute;
  display:inline-block;
  width:16px;
  height:16px;

  content:"";
  background-color:var(--toolbar-icon-bg-color);
  -webkit-mask-size:cover;
          mask-size:cover;
}

.dropdownToolbarButton:is(:hover, :focus-visible, :active)::after{
  background-color:var(--toolbar-icon-hover-bg-color);
}

.toolbarButton::before{
  opacity:var(--toolbar-icon-opacity);
  top:0px;
  left: 0px;
}

.toolbarButton:is(:hover, :focus-visible)::before,
.secondaryToolbarButton:is(:hover, :focus-visible)::before{
  background-color:var(--toolbar-icon-hover-bg-color);
}

.secondaryToolbarButton::before{
  opacity:var(--doorhanger-icon-opacity);
  top:5px;
  inset-inline-start:12px;
}

#sidebarToggle::before{
  -webkit-mask-image:var(--toolbarButton-sidebarToggle-icon);
          mask-image:var(--toolbarButton-sidebarToggle-icon);
  transform:scaleX(var(--dir-factor));
  margin-left: 1px;
  margin-top: 1px;
}

#secondaryToolbarToggle::before{
  -webkit-mask-image:var(--toolbarButton-secondaryToolbarToggle-icon);
          mask-image:var(--toolbarButton-secondaryToolbarToggle-icon);
  transform:scaleX(var(--dir-factor));
}

#findPrevious::before{
  -webkit-mask-image:var(--findbarButton-previous-icon);
          mask-image:var(--findbarButton-previous-icon);
}

#findNext::before{
  -webkit-mask-image:var(--findbarButton-next-icon);
          mask-image:var(--findbarButton-next-icon);
}

#previous::before{
  -webkit-mask-image:var(--toolbarButton-pageUp-icon);
          mask-image:var(--toolbarButton-pageUp-icon);
}

#next::before{
  -webkit-mask-image:var(--toolbarButton-pageDown-icon);
          mask-image:var(--toolbarButton-pageDown-icon);
}

#splitLine {
  width: 1px;
  height: 12px;
  background: #D7D8DD;
  margin-right: 16px;
  margin-top: 2px;
}

/* custom layerChange icon */

#layerChange {
  margin-right: 16px;
}

#layerChange::before{
  cursor: pointer;
  -webkit-mask-image:var(--toolbarButton-layerChange-icon-show);
          mask-image:var(--toolbarButton-layerChange-icon-show);
}

.layerChange-active::before {
  cursor: pointer;
  -webkit-mask-image:var(--toolbarButton-layerChange-icon) !important;
          mask-image:var(--toolbarButton-layerChange-icon) !important;

}


#zoomOut::before{
  cursor: pointer;
  -webkit-mask-image:var(--toolbarButton-zoomOut-icon);
          mask-image:var(--toolbarButton-zoomOut-icon);
}

#zoomIn::before{
  cursor: pointer;
  -webkit-mask-image:var(--toolbarButton-zoomIn-icon);
          mask-image:var(--toolbarButton-zoomIn-icon);
}

#presentationMode::before{
  -webkit-mask-image:var(--toolbarButton-presentationMode-icon);
          mask-image:var(--toolbarButton-presentationMode-icon);
}

#editorFreeText::before{
  -webkit-mask-image:var(--toolbarButton-editorFreeText-icon);
          mask-image:var(--toolbarButton-editorFreeText-icon);
}

#editorHighlight::before{
  -webkit-mask-image:var(--toolbarButton-editorHighlight-icon);
          mask-image:var(--toolbarButton-editorHighlight-icon);
}

#editorInk::before{
  -webkit-mask-image:var(--toolbarButton-editorInk-icon);
          mask-image:var(--toolbarButton-editorInk-icon);
}

#editorStamp::before{
  -webkit-mask-image:var(--toolbarButton-editorStamp-icon);
          mask-image:var(--toolbarButton-editorStamp-icon);
}

:is(#print, #secondaryPrint)::before{
  -webkit-mask-image:var(--toolbarButton-print-icon);
          mask-image:var(--toolbarButton-print-icon);
}

#secondaryOpenFile::before{
  -webkit-mask-image:var(--toolbarButton-openFile-icon);
          mask-image:var(--toolbarButton-openFile-icon);
}

:is(#download, #secondaryDownload)::before{
  -webkit-mask-image:var(--toolbarButton-download-icon);
          mask-image:var(--toolbarButton-download-icon);
}

a.secondaryToolbarButton{
  padding-top:5px;
  text-decoration:none;
}
a:is(.toolbarButton, .secondaryToolbarButton)[href="#"]{
  opacity:0.5;
  pointer-events:none;
}

#viewBookmark::before{
  -webkit-mask-image:var(--toolbarButton-bookmark-icon);
          mask-image:var(--toolbarButton-bookmark-icon);
}

#viewThumbnail::before{
  -webkit-mask-image:var(--toolbarButton-viewThumbnail-icon);
          mask-image:var(--toolbarButton-viewThumbnail-icon);
}

#viewOutline::before{
  -webkit-mask-image:var(--toolbarButton-viewOutline-icon);
          mask-image:var(--toolbarButton-viewOutline-icon);
  transform:scaleX(var(--dir-factor));
}

#viewAttachments::before{
  -webkit-mask-image:var(--toolbarButton-viewAttachments-icon);
          mask-image:var(--toolbarButton-viewAttachments-icon);
}

#viewLayers::before{
  -webkit-mask-image:var(--toolbarButton-viewLayers-icon);
          mask-image:var(--toolbarButton-viewLayers-icon);
}

#currentOutlineItem::before{
  -webkit-mask-image:var(--toolbarButton-currentOutlineItem-icon);
          mask-image:var(--toolbarButton-currentOutlineItem-icon);
  transform:scaleX(var(--dir-factor));
}

#viewFind::before{
  -webkit-mask-image:var(--toolbarButton-search-icon);
          mask-image:var(--toolbarButton-search-icon);
}

.pdfSidebarNotification::after{
  position:absolute;
  display:inline-block;
  top:2px;
  inset-inline-end:2px;
  content:"";
  background-color:rgb(112 219 85);
  height:9px;
  width:9px;
  border-radius:50%;
  /* [custom] */
  display: none;
}

.secondaryToolbarButton{
  position:relative;
  margin:0;
  padding:0 0 1px;
  padding-inline-start:36px;
  height:auto;
  min-height:26px;
  width:auto;
  min-width:100%;
  text-align:start;
  white-space:normal;
  border-radius:0;
  box-sizing:border-box;
  display:inline-block;
}
.secondaryToolbarButton > span{
  padding-inline-end:4px;
}

#firstPage::before{
  -webkit-mask-image:var(--secondaryToolbarButton-firstPage-icon);
          mask-image:var(--secondaryToolbarButton-firstPage-icon);
}

#lastPage::before{
  -webkit-mask-image:var(--secondaryToolbarButton-lastPage-icon);
          mask-image:var(--secondaryToolbarButton-lastPage-icon);
}

#pageRotateCcw::before{
  -webkit-mask-image:var(--secondaryToolbarButton-rotateCcw-icon);
          mask-image:var(--secondaryToolbarButton-rotateCcw-icon);
}

#pageRotateCw::before{
  -webkit-mask-image:var(--secondaryToolbarButton-rotateCw-icon);
          mask-image:var(--secondaryToolbarButton-rotateCw-icon);
}

#cursorSelectTool::before{
  -webkit-mask-image:var(--secondaryToolbarButton-selectTool-icon);
          mask-image:var(--secondaryToolbarButton-selectTool-icon);
}

#cursorHandTool::before{
  -webkit-mask-image:var(--secondaryToolbarButton-handTool-icon);
          mask-image:var(--secondaryToolbarButton-handTool-icon);
}

#scrollPage::before{
  -webkit-mask-image:var(--secondaryToolbarButton-scrollPage-icon);
          mask-image:var(--secondaryToolbarButton-scrollPage-icon);
}

#scrollVertical::before{
  -webkit-mask-image:var(--secondaryToolbarButton-scrollVertical-icon);
          mask-image:var(--secondaryToolbarButton-scrollVertical-icon);
}

#scrollHorizontal::before{
  -webkit-mask-image:var(--secondaryToolbarButton-scrollHorizontal-icon);
          mask-image:var(--secondaryToolbarButton-scrollHorizontal-icon);
}

#scrollWrapped::before{
  -webkit-mask-image:var(--secondaryToolbarButton-scrollWrapped-icon);
          mask-image:var(--secondaryToolbarButton-scrollWrapped-icon);
}

#spreadNone::before{
  -webkit-mask-image:var(--secondaryToolbarButton-spreadNone-icon);
          mask-image:var(--secondaryToolbarButton-spreadNone-icon);
}

#spreadOdd::before{
  -webkit-mask-image:var(--secondaryToolbarButton-spreadOdd-icon);
          mask-image:var(--secondaryToolbarButton-spreadOdd-icon);
}

#spreadEven::before{
  -webkit-mask-image:var(--secondaryToolbarButton-spreadEven-icon);
          mask-image:var(--secondaryToolbarButton-spreadEven-icon);
}

#documentProperties::before{
  -webkit-mask-image:var(--secondaryToolbarButton-documentProperties-icon);
          mask-image:var(--secondaryToolbarButton-documentProperties-icon);
}

.verticalToolbarSeparator{
  display:block;
  margin:5px 2px;
  width:1px;
  height:22px;
  background-color:var(--separator-color);
}
.horizontalToolbarSeparator{
  display:block;
  margin:6px 0;
  height:1px;
  width:100%;
  background-color:var(--doorhanger-separator-color);
}

.toolbarField{
  padding:1px 7px;
  margin:3px 2px;
  border-radius:2px;
  background-color:var(--field-bg-color);
  background-clip:padding-box;
  border:1px solid var(--field-border-color);
  box-shadow:none;
  color:var(--field-color);
  outline:none;
}

.toolbarField[type="checkbox"]{
  opacity:0;
  position:absolute !important;
  left:0;
  margin:10px 0 3px;
  margin-inline-start:7px;
}


#pageNumber{
  -moz-appearance:textfield;
  text-align:center;
  width: fit-content !important;
  background-size:0 0;
  font-size: 14px !important;
  transition-property:none;
  margin-top: 6px;
}

#pageNumber::-webkit-inner-spin-button{
    -webkit-appearance:none;
  }

.loadingInput:has(> #pageNumber.loading)::after{
    display:block;
    visibility:visible;

    transition-property:visibility;
    transition-delay:var(--loading-icon-delay);
  }

.loadingInput::after{
    position:absolute;
    visibility:hidden;
    display:none;
    top:calc(50% - 8px);
    width:16px;
    height:16px;

    content:"";
    background-color:var(--toolbar-icon-bg-color);
    -webkit-mask-size:cover;
            mask-size:cover;
    -webkit-mask-image:var(--loading-icon);
            mask-image:var(--loading-icon);
  }

.loadingInput.start::after{
    inset-inline-start:4px;
  }

.loadingInput.end::after{
    inset-inline-end:4px;
  }

.toolbarField:focus{
  border-color:#0a84ff;
}

.toolbarLabel{
  min-width:16px;
  padding:7px;
  margin:2px;
  padding-top: 10px;
  border-radius:2px;
  color:var(--main-color);
  font-size:12px;
  line-height:14px;
  text-align:left;
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
  cursor:default;
}


#numPages.toolbarLabel{
  min-width: var(--num-page-width);
  margin-right: -4px;
  padding-inline-start:3px;
  font-size: 14px;
}

#thumbnailView,
#outlineView,
#attachmentsView,
#layersView{
  position:absolute;
  width:calc(100% - 8px);
  inset-block:0;
  padding:4px 4px 0;
  overflow:auto;
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
}
#thumbnailView{
  width: 100%;
  padding:10px 0px 0;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;

    /* 滚动条整体部分 */
  &::-webkit-scrollbar {
    scrollbar-gutter: stable;
    background: white !important;
    width: 6px !important;
    height: 6px !important;
  }

  /* 滚动条里面轨道 */
  &::-webkit-scrollbar-track {
    scrollbar-gutter: stable;
    background: white !important;
  }

  /* 滑块 */
  &::-webkit-scrollbar-thumb {
    background-color: #EBECF0 !important;
    scrollbar-gutter: stable;
    border-radius: 10px;
  }

  & > a {
    text-decoration: none;
  }
}

#thumbnailView > a:is(:active, :focus){
  outline:0;
}

.thumbnail{
  --thumbnail-width:0;
  --thumbnail-height:0;

  float:var(--inline-start);
  width:var(--thumbnail-width);
  height:var(--thumbnail-height);
  margin:0 0px 8px;
  padding:1px;
  border:1px solid #EBECF0;
  border-radius:4px;
}

#thumbnailView > a:last-of-type > .thumbnail{
  margin-bottom:10px;
}

/* a:focus > .thumbnail,
.thumbnail:hover{
  border-color:var(--thumbnail-hover-color);
} */

a:hover > .thumbnail,
.thumbnail:hover{
  border-color:var(--thumbnail-hover-color);
}

.thumbnail.selected{
  border-color:var(--thumbnail-selected-color) !important;
}

.thumbnailImage{
  width:var(--thumbnail-width);
  height:var(--thumbnail-height);
  opacity:0.9;
}
a:focus > .thumbnail > .thumbnailImage,
.thumbnail:hover > .thumbnailImage{
  opacity:0.95;
}
.thumbnail.selected > .thumbnailImage{
  opacity:1 !important;
}

.thumbnail:not([data-loaded]) > .thumbnailImage{
  width:calc(var(--thumbnail-width) - 2px);
  height:calc(var(--thumbnail-height) - 2px);
  border:1px dashed rgb(132 132 132);
}

.treeWithDeepNesting > .treeItem,
.treeItem > .treeItems{
  margin-inline-start:20px;
}

.treeItem > a{
  text-decoration:none;
  display:inline-block;
  min-width:calc(100% - 4px);
  height:auto;
  margin-bottom:1px;
  padding:2px 0 5px;
  padding-inline-start:4px;
  border-radius:2px;
  color:var(--treeitem-color);
  font-size:13px;
  line-height:15px;
  -webkit-user-select:none;
     -moz-user-select:none;
          user-select:none;
  white-space:normal;
  cursor:pointer;
}

#layersView .treeItem > a *{
  cursor:pointer;
}
#layersView .treeItem > a > label{
  padding-inline-start:4px;
}
#layersView .treeItem > a > label > input{
  float:var(--inline-start);
  margin-top:1px;
}

.treeItemToggler{
  position:relative;
  float:var(--inline-start);
  height:0;
  width:0;
  color:rgb(255 255 255 / 0.5);
}
.treeItemToggler::before{
  inset-inline-end:4px;
  -webkit-mask-image:var(--treeitem-expanded-icon);
          mask-image:var(--treeitem-expanded-icon);
}
.treeItemToggler.treeItemsHidden::before{
  -webkit-mask-image:var(--treeitem-collapsed-icon);
          mask-image:var(--treeitem-collapsed-icon);
  transform:scaleX(var(--dir-factor));
}
.treeItemToggler.treeItemsHidden ~ .treeItems{
  display:none;
}

.treeItem.selected > a{
  background-color:var(--treeitem-selected-bg-color);
  color:var(--treeitem-selected-color);
}

.treeItemToggler:hover,
.treeItemToggler:hover + a,
.treeItemToggler:hover ~ .treeItems,
.treeItem > a:hover{
  background-color:var(--treeitem-bg-color);
  background-clip:padding-box;
  border-radius:2px;
  color:var(--treeitem-hover-color);
}

#outlineOptionsContainer{
  display:none;
}

#sidebarContainer:has(#outlineView:not(.hidden)) #outlineOptionsContainer{
    display:inherit;
  }

.dialogButton{
  width:auto;
  margin:3px 4px 2px !important;
  padding:2px 11px;
  color:var(--main-color);
  background-color:var(--dialog-button-bg-color);
  border:var(--dialog-button-border) !important;
}

dialog{
  margin:auto;
  padding:15px;
  border-spacing:4px;
  color:var(--main-color);
  font:message-box;
  font-size:12px;
  line-height:14px;
  background-color:var(--doorhanger-bg-color);
  border:1px solid rgb(0 0 0 / 0.5);
  border-radius:4px;
  box-shadow:0 1px 4px rgb(0 0 0 / 0.3);
}
dialog::backdrop{
  background-color:rgb(0 0 0 / 0.2);
}

dialog > .row{
  display:table-row;
}

dialog > .row > *{
  display:table-cell;
}

dialog .toolbarField{
  margin:5px 0;
}

dialog .separator{
  display:block;
  margin:4px 0;
  height:1px;
  width:100%;
  background-color:var(--separator-color);
}

dialog .buttonRow{
  text-align:center;
  vertical-align:middle;
}

dialog :link{
  color:rgb(255 255 255);
}

#passwordDialog{
  text-align:center;
}
#passwordDialog .toolbarField{
  width:200px;
}

#documentPropertiesDialog{
  text-align:left;
}
#documentPropertiesDialog .row > *{
  min-width:100px;
  text-align:start;
}
#documentPropertiesDialog .row > span{
  width:125px;
  word-wrap:break-word;
}
#documentPropertiesDialog .row > p{
  max-width:225px;
  word-wrap:break-word;
}
#documentPropertiesDialog .buttonRow{
  margin-top:10px;
}

.grab-to-pan-grab{
  cursor:grab !important;
}
.grab-to-pan-grab
  *:not(input):not(textarea):not(button):not(select):not(:link){
  cursor:inherit !important;
}
.grab-to-pan-grab:active,
.grab-to-pan-grabbing{
  cursor:grabbing !important;
}
.grab-to-pan-grabbing{
  position:fixed;
  background:rgb(0 0 0 / 0);
  display:block;
  inset:0;
  overflow:hidden;
  z-index:50000;
}

@page{
  margin:0;
}

#printContainer{
  display:none;
}

@media print{
  body{
    background:rgb(0 0 0 / 0) none;
  }
  body[data-pdfjsprinting] #outerContainer{
    display:none;
  }
  body[data-pdfjsprinting] #printContainer{
    display:block;
  }
  #printContainer{
    height:100%;
  }
  #printContainer > .printedPage{
    page-break-after:always;
    page-break-inside:avoid;
    height:100%;
    width:100%;

    display:flex;
    flex-direction:column;
    justify-content:center;
    align-items:center;
  }

  #printContainer > .xfaPrintedPage .xfaPage{
    position:absolute;
  }

  #printContainer > .xfaPrintedPage{
    page-break-after:always;
    page-break-inside:avoid;
    width:100%;
    height:100%;
    position:relative;
  }

  #printContainer > .printedPage :is(canvas, img){
    max-width:100%;
    max-height:100%;

    direction:ltr;
    display:block;
  }
}

.visibleMediumView{
  display:none;
}

@media all and (max-width: 900px){

}

@media all and (max-width: 840px){
  #sidebarContainer{
    background-color:var(--sidebar-narrow-bg-color);
  }
  #outerContainer.sidebarOpen #viewerContainer{
    inset-inline-start:0 !important;
  }
}

@media all and (max-width: 750px){
  :root{
    --editor-toolbar-base-offset:40px;
  }
  #outerContainer .hiddenMediumView{
    display:none;
  }
  #outerContainer .visibleMediumView{
    display:inherit;
  }
}

@media all and (max-width: 690px){
  .hiddenSmallView,
  .hiddenSmallView *{
    /* display:none; */
  }
  .toolbarButtonSpacer{
    width:0;
  }
  .findbar{
    inset-inline-start:34px;
  }
}

@media all and (max-width: 560px){
  #scaleSelectContainer{
    display:none;
  }
}

#odlCustomRatio {
  min-width: 10px;
}

#scaleSelect {
  border: none;
  background: transparent;
}

.odlImageNum {
  text-align: center;
  text-decoration: none;
  margin-bottom: 8px;
  color: rgba(18, 19, 22, 0.80);
}

.odl_selected {
  & > .odlImageNum {
    color: var(--thumbnail-selected-color);
  }
}

#odlScale {
  font-size: 14px;
  margin: 2px 8px 0;
  user-select: none;
}

#toolbarViewerLeft {
  display: flex;
  align-items: center;
}

#sidebarToggle {
  width: 20px;
  height: 20px;
  margin-left: 16px;
  cursor: pointer;
}

#sidebarToggle::before,.toggled::before  {
  width: 18px !important;
  height: 18px !important;
}

#sidebarToggle {
  margin-right: 8px;
  &:active {
    background-color: #F4F5F9 !important;
  }
}

#sidebarToggle.toggled {
  background-color: #F4F5F9;
  border-radius: 2px;
  width: 20px !important;
  height: 20px !important;
}

#sidebarToggle.toggled::before {
  -webkit-mask-image:var(--toolbarButton-sidebarToggle-icon) !important;
  mask-image:var(--toolbarButton-sidebarToggle-icon) !important;
  width: 18px !important;
  height: 18px !important;
  margin-left: 1px;
  margin-top: 1px;

}

#sidebarToggle:is(:hover, :focus-visible) {
  border-radius: 2px;
}

#odl-pdf-title {
  margin-left: 8px;
  margin-top: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100%)
}


@media all and (max-width: 690px){
  #odl-pdf-title {
    max-width: 160px;

  }
}
