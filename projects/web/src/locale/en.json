{"extractor.common.upload": "Click to upload file", "extractor.common.try": "Try：", "extractor.home": "Home", "extractor.button.download": "Download", "extractor.button.lineWrap": "Line Wrap", "extractor.button.fullScreen": "Fullscreen", "extractor.button.exitFullScreen": "Exit Fullscreen", "extractor.button.showLayer": "Display recognition results", "extractor.button.hiddenLayer": "Hide recognition results", "extractor.error": "Extraction failed", "extractor.law": "Please ensure that the documents you upload are legal and compliant, and we do not assume legal responsibility for the content of the documents. Data Protection Policy Children's Information Protection Policy Service Agreement | © All Rights Reserved.Shanghai ICP 2021009351-21", "extractor.button.reUpload": "Re-Upload", "extractor.failed": "Unable to extract, no data to display yet", "extractor.common.extracting": "Extracting, please wait", "extractor.common.extracting.queue": "Queuing for extraction, currently ranked at {id}", "extractor.common.loading": "Loading", "extractor.common.pdf.demo1": "Example1.pdf", "extractor.common.pdf.demo2": "Example2.pdf", "extractor.common.formula.detect.demo1": "FormulaDetail1.jpg", "extractor.common.formula.extract.demo1": "FormulaExtract1.jpg", "extractor.common.login.desc": "Functionality available after login", "extractor.markdown.preview": "Preview", "extractor.markdown.code": "Code", "extractor.home.title": "Welcome to Miner U", "extractor.home.subTitle": "Upload documents and intelligently extract them into Markdown format", "extractor.side.extractTask": "Extraction Tasks", "extractor.side.extractTask.title": "Please upload a PDF document within 5M (within 10 pages) or a JPG/PNG image", "extractor.pdf.title": "PDF Document Extraction", "extractor.pdf.subTitle": "Supports text/scanned PDF parsing, identifies various layout elements and converts them into multimodal Markdown format", "extractor.common.pdf.upload.tip": "Please upload a PDF document", "extractor.pdf.ocr": "OCR Identify Pattern", "extractor.pdf.ocr.popover": "By default, PDF types (text-based, scanned) will be automatically recognized, and based on the recognition results, you can choose to use text recognition or OCR recognition. If enabled, all types of PDF will be recognized by OCR.", "extractor.formula.title2": "Recognize mathematical formulas in images as LaTex format, support multi-line formulas and handwritten formula recognition", "extractor.formula.title": "Locate the formulas within and between rows in the image and generate bounding boxes", "extractor.formula.upload.text": "Click to upload an image", "extractor.formula.popover.extract": "In order to obtain the best formula recognition effect, please crop the image, focus on the formula part, and upload a clear, watermark-free image of the mathematical formula, as shown below", "extractor.formula.popover.detect": "In order to get the best formula recognition effect, please upload clear, non-watermarked images containing mathematical formulas, as shown below", "extractor.formula.upload.accept": "Please upload a JPG/PNG image within 5M", "extractor.formula.upload.try": "Please upload an image containing a mathematical formula Example: ", "extractor.guide.title": "Welcome to use more open source products 🎉", "extractor.queue": "Extract records", "extractor.queue.delete": "Confirm to delete this file?", "extractor.queue.extracting": "Extracting", "extractor.feedback.title1": "Are you satisfied with the overall extraction performance?", "extractor.feedback.title3": "Look forward to your suggestions to help us better optimize", "extractor.feedback.up.title": "What improvements are you expecting to see?", "extractor.feedback.down.title": "What is the reason for your dissatisfaction? ", "extractor.feedback.up": "Satisfied", "extractor.feedback.down": "Dissatisfied", "extractor.feedback.input.placeholder": "Please enter your suggestions for improvement", "extractor.feedback.input.submit": "Submit", "extractor.feedback.success": "Thank you for the feedback", "extractor.queue.delete.success": "Delete successfully"}