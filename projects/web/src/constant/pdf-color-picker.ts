export const PDF_COLOR_PICKER = {
    title: {
      line: 'rgba(121, 124, 255, 1)',
      fill: 'rgba(121, 124, 255, 0.4)'
    },
    text: {
      line: 'rgba(230, 122, 171, 1)',
      fill: 'rgba(230, 122, 171, 0.4)'
    },
    interline_equation: {
      line: 'rgba(240, 240, 124, 1)',
      fill: 'rgba(240, 240, 124, 0.4)'
    },
    discarded: {
      line: 'rgba(164,164,164,1)',
      fill: 'rgba(164,164,164,0.4)'
    },
    image: {
      line: 'rgba(149, 226, 115, 1)',
      fill: 'rgba(149, 226, 115, 0.4)'
    },
    table: {
      line: 'rgba(230, 113, 230, 1)',
      fill: 'rgba(230, 113, 230, 0.4)'
    },
    inline_equation: {
      line: 'rgba(150, 232, 172, 1)',
      fill: 'rgba(150, 232, 172, 0.4)'
    }
  };
  
  export const DEFAULT_COLOR_SECTION = {
    line: 'rgba(166, 113, 230, 1)',
    fill: 'rgba(166, 113, 230, 0.4)'
  };
  
  export const PDF_TEMPLATE_URL_KEY = 't';
  